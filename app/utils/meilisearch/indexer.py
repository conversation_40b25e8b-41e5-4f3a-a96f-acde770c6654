# from meilisearch.errors import Exception

from config.meilisearch import client

from django.http import Http404

from config import settings

from .helpers import (
    clean_params,
    # process_results,
)


def parse_document(doc):
    parsed_document = dict(doc)
    if parsed_document.get('_Document__doc', False):
        del parsed_document['_Document__doc']

    return parsed_document

class MultiSearchClient:
    def __init__(self, indexes):
        self.client = client
        self.indexes = indexes

    def search(self, params):
        query_list = []
        query = params.get('query', '').pop() if type(params.get('query', '')) is list else params.get('query', '')
        for index in self.indexes:
            query_dict = {
                'indexUid': index,
                'q': query,
                # **clean_params(params),
            }
            query_list.append(query_dict)

        response = self.client.multi_search(query_list)

        return response.get('results', [])


class GenericIndexer:
    def __init__(self, index, indexed_id=None):
        self._index = index
        self.indexed_id = indexed_id or index
        self.index = client.index(self._index)

    def get_index_key(self):
        return self._index

    def get_indexed_id(self):
        return self.indexed_id

    def get_index(self):
        """Retrieve index"""
        return self.index

    def list(self):
        try:
            # TODO: query cache first
            return self.index.get_documents()
        except Exception as e:
            if settings.SENTRY_ENABLED:
                from sentry_sdk import capture_message
                capture_message(e)
            print(e)
            raise Http404

    def get(self, pk, include_index=True):
        """Retrieve document by primary key"""
        try:
            # TODO: query cache first
            query = f"{self.indexed_id}-{pk}" if include_index else pk
            return parse_document(self.index.get_document(query))
        except Exception as e:
            if settings.SENTRY_ENABLED:
                from sentry_sdk import capture_message
                capture_message(e)
            raise Http404

    def search(self, params):
        try:
            # TODO: query cache first
            parsed_params = clean_params(params)
            return self.index.search(params.get('query', ''), parsed_params)
        except Exception as e:
            if settings.SENTRY_ENABLED:
                from sentry_sdk import capture_message
                capture_message(e)
            print(e)
            raise Http404

    def insert(self, docs):
        """Add a document"""
        if type(docs) != list:
            docs = [docs]
        self.index.add_documents(docs)

    def update(self, docs):
        """Update a document"""
        if type(docs) != list:
            docs = [docs]
        self.index.update_documents(docs)

    def delete(self, doc_id):
        """Delete a document"""
        self.index.delete_document(doc_id)


class GenericSessionsIndexer(GenericIndexer):
    def __init__(self, sessions_key, index, indexed_id=None):
        self.sessions_key = sessions_key
        super(GenericSessionsIndexer, self).__init__(index, indexed_id)

    @classmethod
    def get_sessions_ids_from_object(cls, obj, sessions_key):
        sessions_ids = set()
        for module in obj.get('modules', []):
            sessions = module.get(sessions_key, [])
            sessions_ids.update(session['slug'] for session in sessions)

        return list(sessions_ids)

    def get_sessions_ids(self, pk):
        if self.sessions_key is not None:
            instance = self.get(pk)
            return self.get_sessions_ids_from_object(
                instance, self.sessions_key)
        return []
