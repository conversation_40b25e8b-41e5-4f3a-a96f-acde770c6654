from django.core.exceptions import ObjectDoesNotExist

from config import settings

from apps.users.models import (
    GiftPurchase,
    PlanOnHold,
    User,
)

from utils.chargebee.users import handle_abandoned_subscription_cart
from utils.hubspot.client import get_membership_list_id
from utils.hubspot.users import (
    subscribe_user as hubspot_subscribe_user,
    unsubscribe_user as hubspot_unsubscribe_user,
    update_user_details as hubspot_update_user_details,
    map_user_billing_address as hubspot_map_user_billing_address,
    update_subscription_cancellation_scheduled as hubspot_update_subscription_cancellation_scheduled,
    update_abandoned_subscription_cart_details as hubspot_update_abandoned_subscription_cart_details,
)



PLAN_TO_MEMBERSHIP_MAP = {}
for community_id in (
        settings.CHARGEBEE_COMMUNITY_MONTHLY_PLAN_IDS +
        settings.CHARGEBEE_COMMUNITY_YEARLY_PLAN_IDS):
    PLAN_TO_MEMBERSHIP_MAP[community_id] = "Community+"
for thrive_id in (
        settings.CHARGEBEE_THRIVE_MONTHLY_PLAN_IDS +
        settings.CHARGEBEE_THRIVE_YEARLY_PLAN_IDS):
    PLAN_TO_MEMBERSHIP_MAP[thrive_id] = "Thrive+"


def membership_tier_changed(previous_plan_id, new_plan_id):
    community_plan_ids = (settings.CHARGEBEE_COMMUNITY_MONTHLY_PLAN_IDS +
                          settings.CHARGEBEE_COMMUNITY_YEARLY_PLAN_IDS)
    thrive_plan_ids = (settings.CHARGEBEE_THRIVE_MONTHLY_PLAN_IDS +
                       settings.CHARGEBEE_THRIVE_YEARLY_PLAN_IDS)
    return previous_plan_id != settings.CHARGEBEE_FREE_PLAN_ID and \
        (previous_plan_id in community_plan_ids and
            new_plan_id in thrive_plan_ids) or \
        (previous_plan_id in thrive_plan_ids and
         new_plan_id in community_plan_ids)


def handle_campaign_coupon(customer, plan_id, coupons):
    hubspot_recipient_gift_data = {}
    campaign_coupons = [c.get('coupon_id') for c in coupons if
                        settings.CHARGEBEE_GIFT_PREFIX in c.get('coupon_id')]
    if (plan_id in settings.CHARGEBEE_THRIVE_YEARLY_PLAN_IDS and
            len(campaign_coupons)):
        gift_purchases = GiftPurchase.objects.filter(
            coupon_code=campaign_coupons[0])
        if gift_purchases.exists():
            gift_purchase = gift_purchases.first()
            gift_purchase.recipient = customer
            gift_purchase.redeemed = True
            gift_purchase.save()
            hubspot_recipient_gift_data = {
                'gift_coupon_code': gift_purchase.coupon_code,
                'gift_bought_by_id': gift_purchase.buyer.hubspot_id,
                'gift_bought_by_name': gift_purchase.buyer.full_name,
            }
            hubspot_buyer_gift_data = {
                'gift_bought_for_id': gift_purchase.recipient.hubspot_id,
                'gift_bought_for_name': gift_purchase.recipient.full_name,
            }
            hubspot_update_user_details(
                gift_purchase.buyer, hubspot_buyer_gift_data)
            # subscribe recipient to gift receivers list
            hubspot_subscribe_user(gift_purchase.recipient,
                                   settings.HUBSPOT_GIFT_RECEIVERS_LIST_ID)

    return hubspot_recipient_gift_data


def enable_plan(data):
    email = data.get('customer').get('email').lower()
    chargebee_id = data.get('customer').get('id')
    subscription = data.get('subscription')
    plan_id = subscription.get('subscription_items')[0].get('item_price_id')
    coupons = subscription.get('coupons', [])
    subscription_id = subscription.get('id')
    billing_address = data.get('customer').get('billing_address')

    try:
        customer = User.objects.get(email=email)
        previous_plan_id = customer.plan_id
        customer.plan_id = plan_id
        customer.chargebee_id = chargebee_id

        if (customer.subscription_id and
                customer.subscription_id != subscription_id):
            print(f"chargebee.enable_plan: {customer.email}"
                  f"[{customer.chargebee_id}] subscription id updated: "
                  f"({customer.subscription_id} -> {subscription_id})")

        customer.subscription_id = subscription_id

        customer.community_groups.extend(settings.NODEBB_COMMUNITY_GROUPS)
        if 'thrive' in plan_id.lower():
            customer.community_groups.append('Thrive')

        customer.save()
        print(f"chargebee.enable_plan: {customer.email}"
              f"[{customer.chargebee_id}] to subscription plan "
              f"'{customer.plan_id}' ({customer.subscription_id})")

        PlanOnHold.objects.filter(
            on_hold=True,
            email=email).update(on_hold=False)

        hubspot_update_data = hubspot_map_user_billing_address(billing_address)
        hubspot_update_data.update({'subscription_id': subscription_id})
        # handle coupon recipient for campaign
        if coupons:
            hubspot_recipient_gift_data = handle_campaign_coupon(
                customer, plan_id, coupons)
            hubspot_update_data.update(hubspot_recipient_gift_data)

        if membership_tier_changed(previous_plan_id, plan_id):
            hubspot_update_data.update({'membership_change': True})
        hubspot_update_user_details(customer, hubspot_update_data)
        hubspot_unsubscribe_user(customer, get_membership_list_id(
            previous_plan_id))
        hubspot_subscribe_user(customer, get_membership_list_id(plan_id))
        hubspot_update_abandoned_subscription_cart_details(customer)
    except ObjectDoesNotExist:
        PlanOnHold.objects.create(
            email=email,
            chargebee_id=chargebee_id,
            plan_id=plan_id,
            subscription_id=subscription_id,
            event=data)
        error_message = f"Chargebee: {email} does not exist."
        if settings.SENTRY_ENABLED:
            from sentry_sdk import capture_message
            capture_message(error_message)
        print(error_message)


def disable_plan(data):
    email = data.get('customer').get('email').lower()
    chargebee_id = data.get('customer').get('id')
    subscription = data.get('subscription')
    plan_id = subscription.get('subscription_items')[0].get('item_price_id')
    subscription_id = subscription.get('id')

    try:
        # uses email to get user instead of chargebee_id
        customer = User.objects.get(email=email)

        if customer.subscription_id != subscription_id:
            error_message = (f"chargebee-disable-plan: {email}:"
                             f"{customer.subscription_id} existing "
                             f"subscription id different from cancelled "
                             f"subscription id ({subscription_id})")
            if settings.SENTRY_ENABLED:
                from sentry_sdk import capture_message
                capture_message(error_message)
            print(error_message)
            return
        previous_plan_id = customer.plan_id
        customer.plan_id = settings.CHARGEBEE_FREE_PLAN_ID

        # TODO: test user removal from groups and permissions in Community
        try:
            user_plan_content = customer.content.filter(on_demand=False)
            user_plan_community_groups = []
            for content in user_plan_content:
                content.is_active = False
                user_plan_community_groups.append(
                    content.object_data.get('community_group_name'))
                content.save()
                # unsubscribes user from content Hubspot lists
                hubspot_unsubscribe_user(
                    customer, content.object_data.get('hubspot_list_id'))
            # remove user from community groups
            for community_group in user_plan_community_groups:
                customer.community_groups.remove(community_group)
            # removes from Thrive group anyway
            customer.community_groups.remove("Thrive")
        except Exception as e:
            error_message = "Chargebee:disable_plan: failed updating user content."
            print(error_message)
            if settings.SENTRY_ENABLED:
                from sentry_sdk import capture_message
                capture_message(error_message)

        customer.save()
        print(f"chargebee.disable_plan: {customer.email} "
              f"from {previous_plan_id} to {customer.plan_id}")

        PlanOnHold.objects.filter(
            on_hold=True,
            email=email).update(on_hold=False)

        hubspot_update_user_details(customer, {
            'membership_cancellation': True,
            'membership_previous': PLAN_TO_MEMBERSHIP_MAP.get(previous_plan_id),
        })
        hubspot_unsubscribe_user(customer, get_membership_list_id(
            previous_plan_id))
        # unsubscribe user from abandoned cart lists
        if customer.plan_id in settings.CHARGEBEE_THRIVE_YEARLY_PLAN_IDS:
            hubspot_unsubscribe_user(
                customer, settings.HUBSPOT_ABANDONED_CART_YEARLY_LIST_ID)
        else:
            hubspot_unsubscribe_user(
                customer, settings.HUBSPOT_ABANDONED_CART_QUARTERLY_LIST_ID)
        hubspot_subscribe_user(customer, get_membership_list_id(
            settings.CHARGEBEE_FREE_PLAN_ID))
        # create membership cancellation coupon
        handle_abandoned_subscription_cart(
            customer, previous_plan_id, cancelled=True)
    except ObjectDoesNotExist:
        # PlanOnHold.objects.create(
        #     email=email,
        #     chargebee_id=chargebee_id,
        #     plan_id=plan_id,
        #     on_hold=False,
        #     event=data)
        error_message = f"Chargebee: {email} does not exist."
        if settings.SENTRY_ENABLED:
            from sentry_sdk import capture_message
            capture_message(error_message)
        print(error_message)


def cancellation_scheduled(data):
    email = data.get('customer').get('email').lower()
    chargebee_id = data.get('customer').get('id')

    try:
        customer = User.objects.get(email=email)

        try:
            hubspot_update_subscription_cancellation_scheduled(customer)
        except Exception as e:
            error_message = f"Chargebee:cancellation_scheduled: {email}."
            print(error_message)
            if settings.SENTRY_ENABLED:
                from sentry_sdk import capture_message
                capture_message(error_message)

    except ObjectDoesNotExist:
        error_message = f"Chargebee: {email} does not exist."
        if settings.SENTRY_ENABLED:
            from sentry_sdk import capture_message
            capture_message(error_message)
        print(error_message)


def cancellation_scheduled_removed(data):
    email = data.get('customer').get('email').lower()
    chargebee_id = data.get('customer').get('id')

    try:
        customer = User.objects.get(email=email)

        try:
            hubspot_update_subscription_cancellation_scheduled(customer, False)
        except Exception as e:
            error_message = f"Chargebee:cancellation_scheduled_removed: {email}."
            print(error_message)
            if settings.SENTRY_ENABLED:
                from sentry_sdk import capture_message
                capture_message(error_message)

    except ObjectDoesNotExist:
        error_message = f"Chargebee: {email} does not exist."
        if settings.SENTRY_ENABLED:
            from sentry_sdk import capture_message
            capture_message(error_message)
        print(error_message)


def get_user_plan(user):
    return getattr(user, 'plan', settings.CHARGEBEE_FREE_PLAN_ID)


event_dispatcher = {
    'subscription_activated': enable_plan,
    'subscription_changed': enable_plan,
    'subscription_created': enable_plan,
    'subscription_reactivated': enable_plan,
    'subscription_renewed': enable_plan,
    'subscription_resumed': enable_plan,
    'subscription_started': enable_plan,
    'subscription_cancelled': disable_plan,
    'subscription_deleted': disable_plan,
    'subscription_paused': disable_plan,
    'subscription_cancellation_scheduled': cancellation_scheduled,
    'subscription_scheduled_cancellation_removed': cancellation_scheduled_removed,
}
