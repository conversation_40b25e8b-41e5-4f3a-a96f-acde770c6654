from django.contrib.auth import get_user_model
from django.core.exceptions import ObjectDoesNotExist

from config import settings

from apps.user_content.models import UserContent

from utils.api.enrolment import enrol_user
from utils.api.products import get_minimum_product_data
from utils.chargebee.users import (
    generate_coupon_code_for_subscription_plan,
    generate_coupon_code_for_product,
    get_customer_email,
)
from utils.hubspot.users import (
    map_user_billing_address as hubspot_map_user_billing_address,
    map_user_latest_purchase as hubspot_map_user_latest_purchase,
    update_abandoned_purchase_cart_details as hubspot_update_abandoned_purchase_cart_details,
    update_user_details as hubspot_update_user_details,
    update_gift_coupon_code as hubspot_update_gift_coupon_code,
    subscribe_user as hubspot_subscribe_user,
)
from utils.meilisearch.router import content_type_index_mapper as index_mapper

from apps.users.models import GiftPurchase



User = get_user_model()


def handle_product_purchase(chargebee_entity_id, content_type, user, address):
    try:
        user_products = user.content.filter(
            chargebee_entity_id=chargebee_entity_id)

        if user_products.exists():
            user_content = user_products.first()
        else:
            # query Meilisearch using chargebee_entity_id field to get object
            products = index_mapper[content_type].search(
                {'chargebee_entity_id': chargebee_entity_id})
            if len(products.get('hits')):
                product = products.get('hits')[0]
            else:
                raise ObjectDoesNotExist()

            user_content_data = {
                "user": user,
                "content_type": content_type,
                "chargebee_entity_id": chargebee_entity_id,
            }

            # create UserContent object
            user_content = UserContent.objects.get(**user_content_data)

            user_content.index_id = product.get('id')
            user_content.description = product.get('title')
            user_content.slug = product.get('slug')
            user_content.object_data = get_minimum_product_data(product)

        user_content.is_active = True
        user_content.on_demand = True
        user_content.save()

        if content_type in ['courses', 'events']:
            enrol_user(
                user_content.object_data, content_type, user,
                strip_product=False)

        hubspot_data = hubspot_map_user_billing_address(address)
        if content_type == 'courses':
            hubspot_data.update(hubspot_map_user_latest_purchase(
                user_content.object_data))
        hubspot_update_user_details(user, hubspot_data)
        hubspot_update_abandoned_purchase_cart_details(user)

        success_message = f"Chargebee purchase updated for " \
                          f"{user.chargebee_id}:{user.email} " \
                          f"[{user_content.content_type}][{chargebee_entity_id}]"
        print(success_message)
    except ObjectDoesNotExist as e:
        error_message = f"Chargebee: handle product single purchase " \
                        f"'{content_type}' product not found: " \
                        f"{chargebee_id} [{chargebee_entity_id}]: {e}"
        print(error_message)
        if settings.SENTRY_ENABLED:
            from sentry_sdk import capture_message
            capture_message(error_message)
    except Exception as e:
        error_message = f"Chargebee: handle product single purchase " \
                        f"{user.chargebee_id} [{content_type}][{chargebee_entity_id}]: {e}"
        print(error_message)
        if settings.SENTRY_ENABLED:
            from sentry_sdk import capture_message
            capture_message(error_message)


def handle_gift_purchase(user):
    # generate coupon code
    coupon = generate_coupon_code_for_subscription_plan(
        name_prefix="Gift: Thrive 1 Year - ", id_prefix="GIFT-THRIVE-",
        user=user)

    # save coupon code in user profile (to cross later)
    GiftPurchase.objects.create(buyer=user, coupon_code=coupon.get('id'))
    # update Hubspot custom property with coupon code
    hubspot_update_gift_coupon_code(user, coupon.get('id'), giver=True)
    # subscribe buyer to gift givers list
    hubspot_subscribe_user(user, settings.HUBSPOT_GIFT_GIVERS_LIST_ID)



def create_single_purchase(data):
    """
    Add user content upon single purchase from Chargebee request.

    If the user doesn't already exist, it's created on the fly.

    :param data: invoice data from Chargebee webhook event 'invoice_created'
    """
    if data.get('invoice').get('status') != 'paid':
        return None
    items = data.get('invoice').get('line_items', [])
    if not len(items) or items[0].get('entity_type') != 'charge_item_price':
        return None

    charge = items[0]
    chargebee_id = charge.get('customer_id')
    chargebee_entity_id = charge.get('entity_id')
    if 'gift' in chargebee_entity_id.lower():
        content_type = 'gift'
    else:
        content_type, _ = chargebee_entity_id.split('_')
    billing_address = data.get('invoice').get('billing_address')

    try:
        users = User.objects.filter(chargebee_id=chargebee_id)
        if users.exists():
            user = users.first()
        else:
            email = get_customer_email(chargebee_id)
            user = User.objects.get(email=email)
            user.chargebee_id = chargebee_id
            user.save()

        if chargebee_entity_id == settings.CHARGEBEE_SUBSCRIPTION_GIFT_ID:
            handle_gift_purchase(user)
        else:
            handle_product_purchase(
                chargebee_entity_id, content_type, user, billing_address)
    except ObjectDoesNotExist as e:
        error_message = f"Chargebee create single purchase " \
                        f"'{content_type}' product not found: " \
                        f"{chargebee_id} [{chargebee_entity_id}]: {e}"
        print(error_message)
        if settings.SENTRY_ENABLED:
            from sentry_sdk import capture_message
            capture_message(error_message)
    except Exception as e:
        error_message = f"Chargebee create single purchase " \
                        f"{chargebee_id} [{content_type}][{chargebee_entity_id}]: {e}"
        print(error_message)
        if settings.SENTRY_ENABLED:
            from sentry_sdk import capture_message
            capture_message(error_message)

event_dispatcher = {
    'invoice_generated': create_single_purchase,
    'invoice_updated': create_single_purchase,
}
