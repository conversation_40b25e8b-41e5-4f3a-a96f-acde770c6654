import json
import uuid
from datetime import datetime, timedelta

from django.utils.text import slugify

from config import settings
from config.exceptions import InvalidRequest

from .client import (
    chargebee,
    ENABLED,
)
from utils.hubspot.users import (
    update_abandoned_purchase_cart_details as hubspot_update_abandoned_purchase_cart_details,
    update_abandoned_subscription_cart_details as hubspot_update_abandoned_subscription_cart_details,
)
from .logic import get_extra_params_for_single_purchase



def get_customer(chargebee_id):
    try:
        result = chargebee.Customer.retrieve(chargebee_id)

        return result.customer
    except Exception as e:
        error_message = f"Chargebee get customer " \
                        f"{chargebee_id}: {e}"
        print(error_message)
        if settings.SENTRY_ENABLED:
            from sentry_sdk import capture_message
            capture_message(error_message)
        return {}


def get_customer_email(chargebee_id):
    try:
        return get_customer(chargebee_id).email
    except Exception as e:
        error_message = f"Chargebee get customer email " \
                        f"{chargebee_id}: {e}"
        print(error_message)
        if settings.SENTRY_ENABLED:
            from sentry_sdk import capture_message
            capture_message(error_message)
        return {}


def update_user_details(user):
    try:
        data = {
            "first_name" : user.first_name,
            "last_name" : user.last_name,
            "email" : user.email,
        }

        if ENABLED:
            response = chargebee.Customer.update(user.chargebee_id, data)

            return response
        print("chargebee:update-user-details: "
              f"{user.chargebee_id}:{user.email} disabled")
        return {}
    except Exception as e:
        error_message = f"Chargebee update user details " \
                        f"{user.chargebee_id}:{user.email}: {e}"
        print(error_message)
        if settings.SENTRY_ENABLED:
            from sentry_sdk import capture_message
            capture_message(error_message)
        return {}


def retrieve_coupon_code(code):
    """
    Retrieve a discount coupon from code.
    See more https://apidocs.eu.chargebee.com/docs/api/coupons?prod_cat_ver=2#retrieve_a_coupon

    :param code: string for coupon code
    :return: coupon object
    """
    try:
        if ENABLED:
            response = chargebee.Coupon.retrieve(code)
            coupon = json.loads(str(response.coupon))

            return coupon
        print("chargebee.retrieve-coupon-code: disabled")
        return {}
    except Exception as e:
        error_message = f"Chargebee retrieve coupon code " \
                        f"{code}: {e}"
        print(error_message)
        if settings.SENTRY_ENABLED:
            from sentry_sdk import capture_message
            capture_message(error_message)
        return {}


def validate_coupon(coupon):
    try:
        return coupon.get('status') == 'active'
    except Exception as e:
        error_message = f"Chargebee validate coupon code " \
                        f"{coupon}: {e}"
        print(error_message)
        if settings.SENTRY_ENABLED:
            from sentry_sdk import capture_message
            capture_message(error_message)
        return False


def generate_coupon_code_for_subscription_plan(
        name_prefix, id_prefix,
        plan_ids=settings.CHARGEBEE_THRIVE_YEARLY_PLAN_IDS, period=1,
        period_unit="year", metadata=None,
        user=None):
    """
    Generate a discount coupon for a subscription plan.
    See more https://apidocs.eu.chargebee.com/docs/api/coupons?prod_cat_ver=2#retrieve_a_coupon

    :param plan_ids: plan ids associated with the coupon
    :param period: period of the discount, default is 1
    :param period_unit: period unit of the discount, default is year
    :param metadata: extra metadata to be associated with the coupon
    :param user: user that created the coupon, currently
    :return: coupon object
    """
    if metadata is None:
        metadata = {}
    try:
        if ENABLED:
            uuid_hex = uuid.uuid4().hex[:8]
            coupon_id = f"{id_prefix}{uuid_hex}"
            data = {
                "id": coupon_id,
                "name": f"{name_prefix}{uuid_hex}",
                "discount_percentage": "100.0",
                "discount_type": "percentage",
                "duration_type": "limited_period",
                "period": period,
                "period_unit": period_unit,
                "max_redemptions": 1,
                "apply_on": "each_specified_item",
                "item_constraints": [
                    {
                        "constraint": "specific",
                        "item_type": "plan",
                        "item_price_ids": json.dumps(plan_ids),
                    }],
            }
            if metadata:
                data.update({"meta_data": metadata})
            response = chargebee.Coupon.create_for_items(data)
            coupon = json.loads(str(response.coupon))

            return coupon
        print("chargebee.create-subscription-plan-coupon-code: disabled")
        return {}
    except Exception as e:
        error_message = f"Chargebee create subscription plan coupon " \
                        f"{coupon_id}: {e}"
        print(error_message)
        if settings.SENTRY_ENABLED:
            from sentry_sdk import capture_message
            capture_message(error_message)
        return {}


def generate_coupon_code_for_product(
        product, period=1, period_unit="week",
        percentage=float(settings.CHARGEBEE_ABANDONED_CART_DISCOUNT_PERCENTAGE),
        metadata={}):
    """
    Generate a discount coupon for a subscription plan.
    See more https://apidocs.eu.chargebee.com/docs/api/coupons?prod_cat_ver=2#retrieve_a_coupon

    :param product: product associated with the coupon
    :param period: period of the discount, default is 1
    :param period_unit: period unit of the discount, default is week
    :param percentage: percentage of the discount, default is 15%
    :param metadata: extra metadata to be associated with the coupon
    :return: coupon object
    """
    uuid_hex = uuid.uuid4().hex[:8]
    title = slugify(product.get("title")).upper()
    coupon_id = f"GIFT-{title}-{uuid_hex}"
    try:
        if ENABLED:
            name = (f"{product.get('content_type').capitalize()[:-1]}: "
                    f"{product.get("title")[:28]} {str(int(percentage))}% {uuid_hex}")
            item_ids = [product.get('chargebee_entity_id')]
            valid_till = int((datetime.now() + timedelta(
                days=int(settings.CHARGEBEE_ABANDONED_CART_DISCOUNT_VALID_DAYS))).timestamp())
            data = {
                "id": coupon_id,
                "name": name,
                "discount_percentage": str(percentage),
                "discount_type": "percentage",
                "valid_till": valid_till,
                "max_redemptions": 1,
                "duration_type": "one_time",
                "apply_on": "each_specified_item",
                "item_constraints": [
                    {
                        "constraint": "specific",
                        "item_type": "charge",
                        "item_price_ids": json.dumps(item_ids),
                    }],
            }
            if metadata:
                data.update({"meta_data": metadata})
            response = chargebee.Coupon.create_for_items(data)
            coupon = json.loads(str(response.coupon))

            return coupon
        print("chargebee.create-purchase-coupon-code: disabled")
        return {}
    except Exception as e:
        error_message = f"Chargebee create purchase coupon " \
                        f"{coupon_id}: {e}"
        print(error_message)
        if settings.SENTRY_ENABLED:
            from sentry_sdk import capture_message
            capture_message(error_message)
        return {}


def create_portal_session(user):
    """
    Should create a portal session for a user.
    See more

    :param portal_session_id: existing portal session id
    :return: hosted page object
    """
    try:
        if ENABLED:
            data = {
                "redirect_url" : settings.BASE_FRONTEND_URL,
            }
            if user and user.is_authenticated:
                if user.chargebee_id:
                    customer_data = { "id": user.chargebee_id }
                else:
                    customer_data = {
                        "email": user.email,
                        "first_name": user.first_name,
                        "last_name": user.last_name,
                    }
                data["customer"] = customer_data

            response = chargebee.PortalSession.create(data)
            parsed = json.loads(str(response))

            return parsed.get('portal_session')
        print(f"chargebee.create-portal-session: "
              f"{user.chargebee_id}:{user.email} disabled")
        return {}
    except Exception as e:
        error_message = f"Create portal session: " \
                        f"{user.chargebee_id}:{user.email} {e}"
        print(error_message)
        if settings.SENTRY_ENABLED:
            from sentry_sdk import capture_message
            capture_message(error_message)
        raise InvalidRequest(detail=str(e))


def retrieve_portal_session(portal_session_id):
    """
    Should retrieve an existing portal session.
    See more https://apidocs.eu.chargebee.com/docs/api/hosted_pages?prod_cat_ver=2&lang=python#retrieve_a_hosted_page

    :param portal_session_id: existing portal session id
    :return: hosted page object
    """
    try:
        if ENABLED:
            response = chargebee.PortalSession.retrieve(portal_session_id)

            parsed = json.loads(str(response))

            return parsed.get('portal_session')
        print("chargebee.retrieve-portal-session: disabled")
        return {}
    except Exception as e:
        error_message = f"Retrieve portal session: {e}"
        print(error_message)
        if settings.SENTRY_ENABLED:
            from sentry_sdk import capture_message
            capture_message(error_message)
        raise InvalidRequest(detail=str(e))


def generate_subscription_hosted_page(data, user):
    """
    Should retrieve an existing hosted page.
    See more https://apidocs.eu.chargebee.com/docs/api/hosted_pages?prod_cat_ver=2&lang=python#create_checkout_for_a_new_subscription

    :param data: product object from Meilisearch (including the "content_type")
    :param user: User instance
    :return: hosted page object
    """
    try:
        item_price_id = data.get("plan-id")
        coupon = data.get("coupon")
        if ENABLED:
            subscription_data = {
                "subscription_items": [{
                    "item_price_id" : item_price_id,
                }],
            }
            if coupon:
                subscription_data["coupon_ids"] = [coupon]
            if user and user.is_authenticated:
                if user.chargebee_id:
                    customer_data = { "id": user.chargebee_id }
                else:
                    customer_data = {
                        "email": user.email,
                        "first_name": user.first_name,
                        "last_name": user.last_name,
                    }
                subscription_data["customer"] = customer_data

            response = chargebee.HostedPage.checkout_new_for_items(
                subscription_data)

            return response._response.get('hosted_page')
        print(f"chargebee.generate-subscription-hosted-page: "
              f"{item_price_id} disabled")
        return {}
    except Exception as e:
        error_message = f"Generate subscription hosted page: {e}"
        print(error_message)
        if settings.SENTRY_ENABLED:
            from sentry_sdk import capture_message
            capture_message(error_message)
        raise InvalidRequest(detail=str(e))


def update_subscription_hosted_page(data, user):
    """
    Should retrieve an existing hosted page.
    See more https://apidocs.eu.chargebee.com/docs/api/hosted_pages?prod_cat_ver=2&lang=python#create_checkout_to_update_a_subscription

    :param data: product object from Meilisearch (including the "content_type")
    :param user: User instance
    :return: hosted page object
    """
    try:
        item_price_id = data.get("new-plan-id")
        if ENABLED:
            data = {
                "subscription" : {
                    "id" : user.subscription_id,
                },
                "subscription_items" : [{
                    "item_price_id" : item_price_id,
                }],
                "customer" : {
                    "id": user.chargebee_id,
                }
            }

            response = chargebee.HostedPage.checkout_existing_for_items(data)

            return response._response.get('hosted_page')
        print("chargebee.retrieve-subscription-hosted-page: "
              f"{item_price_id} {user.chargebee_id} disabled")
        return {}
    except Exception as e:
        error_message = f"Retrieve subscription hosted page: {e}"
        print(error_message)
        if settings.SENTRY_ENABLED:
            from sentry_sdk import capture_message
            capture_message(error_message)
        raise InvalidRequest(detail=str(e))


def generate_single_purchase_hosted_page(product, user, price_percent=False):
    """
    Should create a charge item on Chargebee.
    See more https://apidocs.eu.chargebee.com/docs/api/hosted_pages?prod_cat_ver=2&lang=python#checkout_charge-items_and_one-time_charges

    :param product: product object from Meilisearch
    :param user: User instance
    :return: hosted page object
    """
    entity_id = product.get("chargebee_entity_id")
    price = int(product.get("price") * 100)
    unit_price = price / 100 if price_percent else price
    try:
        data = {
            "currency_code": "GBP",
            "item_prices" : [
                {
                    "item_price_id": entity_id,
                    "unit_price": unit_price,
                    "quantity": 1,
                }
            ],
            **get_extra_params_for_single_purchase(product, user),
        }
        if user.chargebee_id is not None:
            customer_data = { "id": user.chargebee_id }
        else:
            customer_data = {
                "email": user.email,
                "first_name": user.first_name,
                "last_name": user.last_name,
            }
        data["customer"] = customer_data

        if ENABLED:
            response = chargebee.HostedPage.checkout_one_time_for_items(data)
            return response._response.get('hosted_page')
        print("chargebee.generate-single-purchase-hosted-page: "
              f"{user.chargebee_id}:[{entity_id}]({product.get('title')}) disabled")
        return {}
    except Exception as e:
        error_message = f"Generate single purchase hosted page: " \
                        f"{user.chargebee_id}:[{entity_id}] {e}"
        print(error_message)
        if settings.SENTRY_ENABLED:
            from sentry_sdk import capture_message
            capture_message(error_message)
        raise InvalidRequest(detail=str(e))


def get_chargebee_product_price(chargebee_entity_id):
    result = chargebee.ItemPrice.retrieve(chargebee_entity_id)

    return result.item_price.price


def handle_abandoned_product_cart(user, product):
    # generate coupon code
    coupon = generate_coupon_code_for_product(product)
    hubspot_update_abandoned_purchase_cart_details(
        user, product, coupon_code=coupon.get('id'))


def handle_abandoned_subscription_cart(user, plan_id, cancelled=False):
    # generate coupon code
    if plan_id in settings.CHARGEBEE_THRIVE_YEARLY_PLAN_IDS:
        coupon_code = settings.HUBSPOT_ABANDONED_CART_YEARLY_COUPON_CODE
        # coupon_name_prefix = f"Yearly - First Year 15% "
        # coupon_id_prefix = 'GIFT-YEARLY15-'
        # plan_ids = settings.CHARGEBEE_THRIVE_YEARLY_PLAN_IDS
        # period_unit = "year"
        # period = 1
    # if plan_id in settings.CHARGEBEE_THRIVE_MONTHLY_PLAN_IDS:
    else:
        coupon_code = settings.HUBSPOT_ABANDONED_CART_QUARTERLY_COUPON_CODE
        # coupon_name_prefix = f"Quarterly - First Quarter 10% "
        # coupon_id_prefix = 'GIFT-QUARTERLY10-'
        # plan_ids = settings.CHARGEBEE_THRIVE_MONTHLY_PLAN_IDS
        # period_unit = "month"
        # period = 3
    # coupon = generate_coupon_code_for_subscription_plan(
    #     coupon_name_prefix, coupon_id_prefix,
    #     plan_ids, period, period_unit,
    #     user=user)
    hubspot_update_abandoned_subscription_cart_details(
        user, plan_id, coupon_code, cancelled)


# def generate_on_demand_purchase_hosted_page(data, user):
#     try:
#         slug = data.get("slug")
#         title = data.get("title")
#         price = int(data.get("price") * 100)
#         content_type = data.get("content_type")
#
#         data = {
#             "subscription_items" : [
#                 {
#                     "item_price_id": "on-demand-usd-yearly",
#                     "unit_price": 0,
#                     "quantity": 1,
#                 },
#                 {
#                     "item_price_id": "single-purchase",
#                     "unit_price": price,
#                     "quantity": 1,
#                     # "charge_once": True,
#                     "description": title,
#                     "cf_title": title,
#                     "cf_slug": slug,
#                     "cf_content_type": data.get("content_type"),
#                 }
#             ]
#         }
#         if user.chargebee_id is not None:
#             data["customer[id]"] = user.chargebee_id
#         else:
#             data["customer[email]"] = user.email
#             data["customer[first_name]"] = user.first_name
#             data["customer[last_name]"] = user.last_name
#         from pprint import pprint;pprint(data)
#         if ENABLED:
#             response = chargebee.HostedPage.checkout_new_for_items(data)
#             return response._response.get('hosted_page')
#         print("chargebee.generate-single-purchase-hosted-page: "
#               f"{user.chargebee_id}:{slug} disabled")
#         return {}
#     except Exception as e:
#         error_message = f"Generate single purchase hosted page: {e}"
#         print(error_message)
#         # if not settings.DEBUG:
#         #     from sentry_sdk import capture_message
#         #     capture_message(error_message)
#         raise InvalidRequest(detail=str(e))
