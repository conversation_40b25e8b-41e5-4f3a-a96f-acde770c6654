from datetime import datetime, timezone
from django.contrib.auth import get_user_model
from django.db.models import Q

from apps.users.models import Profile
from apps.user_content.models import UserContent, UserEnrolment, SessionUserContent


User = get_user_model()


def parse_timestamp(timestamp_str):
    """Parse timestamp string in format '%Y%m%d_%H%M%S' to datetime object"""
    if not timestamp_str:
        return None
    try:
        return datetime.strptime(timestamp_str, '%Y%m%d_%H%M%S').replace(tzinfo=timezone.utc)
    except ValueError:
        raise ValueError(f"Invalid timestamp format. Expected '%Y%m%d_%H%M%S', got '{timestamp_str}'")


def get_default_timestamps():
    """Get default from_timestamp (beginning of previous day) and until_timestamp (now)"""
    now = datetime.now(timezone.utc)

    # Default from_timestamp: beginning of previous day (current day - 1)
    from_timestamp = (now - timezone.timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)

    # Default until_timestamp: now
    until_timestamp = now

    return from_timestamp, until_timestamp


def collect_user_events(from_timestamp, until_timestamp):
    """
    Collect all user-related events within the specified timestamp range.

    Returns a dictionary with the following structure:
    {
        'users_created': [...],
        'users_updated': [...],
        'profiles_updated': [...],
        'user_content_created': [...],
        'user_content_updated': [...],
        'user_enrolments_created': [...],
        'user_enrolments_updated': [...],
        'session_user_content_created': [...],
        'session_user_content_updated': [...]
    }
    """

    # Helper function to serialize user data
    def serialize_user(user):
        return {
            'id': str(user.id),
            'email': user.email,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'created': user.created.isoformat(),
            'is_staff': user.is_staff,
            'is_admin': user.is_admin,
            'chargebee_id': user.chargebee_id,
            'subscription_id': user.subscription_id,
            'plan_id': user.plan_id,
            'is_active': user.is_active,
            'is_verified': user.is_verified,
            'activation_token': user.activation_token,
            'password_reset_token': user.password_reset_token,
            'community_user_id': user.community_user_id,
            'community_user_username': user.community_user_username,
            'community_groups': user.community_groups,
            'hubspot_id': user.hubspot_id,
            'subscribed_to_newsletter': user.subscribed_to_newsletter,
        }

    # Helper function to serialize profile data
    def serialize_profile(profile):
        return {
            'id': profile.id,
            'user_id': str(profile.user.id),
            'created': profile.created.isoformat(),
            'updated': profile.updated.isoformat(),
            'picture': profile.picture.url if profile.picture else None,
            'picture_tiny': profile.picture_tiny.url if profile.picture_tiny else None,
            'cover': profile.cover.url if profile.cover else None,
            'privacy_rules': profile.privacy_rules,
            'private_details': profile.private_details,
            'member_details': profile.member_details,
            'friends_details': profile.friends_details,
            'public_details': profile.public_details,
            'interests': profile.interests,
            'newsletters': profile.newsletters,
            'social_networks': profile.social_networks,
        }

    # Helper function to serialize user content data
    def serialize_user_content(content):
        return {
            'id': str(content.id),
            'user_id': str(content.user.id),
            'created': content.created.isoformat(),
            'updated': content.updated.isoformat(),
            'content_type': content.content_type,
            'slug': content.slug,
            'index_id': content.index_id,
            'description': content.description,
            'chargebee_entity_id': content.chargebee_entity_id,
            'object_data': content.object_data,
            'bookmarked': content.bookmarked,
            'completed': content.completed,
            'on_demand': content.on_demand,
            'is_active': content.is_active,
        }

    # Helper function to serialize user enrolment data
    def serialize_user_enrolment(enrolment):
        return {
            'id': str(enrolment.id),
            'user_id': str(enrolment.user.id),
            'content_id': str(enrolment.content.id),
            'created': enrolment.created.isoformat(),
            'updated': enrolment.updated.isoformat(),
            'is_active': enrolment.is_active,
            'completed_sessions': enrolment.completed_sessions,
            'progress': enrolment.progress,
            'is_completed': enrolment.is_completed,
            'completed_on': enrolment.completed_on.isoformat() if enrolment.completed_on else None,
        }

    # Helper function to serialize session user content data
    def serialize_session_user_content(session_content):
        return {
            'id': str(session_content.id),
            'user_id': str(session_content.user.id),
            'created': session_content.created.isoformat(),
            'updated': session_content.updated.isoformat(),
            'content_type': session_content.content_type,
            'slug': session_content.slug,
            'index_id': session_content.index_id,
            'description': session_content.description,
            'object_data': session_content.object_data,
            'bookmarked': session_content.bookmarked,
            'completed': session_content.completed,
        }

    # Collect new users created
    users_created = User.objects.filter(
        created__gte=from_timestamp,
        created__lte=until_timestamp
    )

    # Note: User model doesn't have an 'updated' field, so we can't track user updates
    # This would require adding an 'updated' field to the User model or using a different approach
    users_updated = User.objects.none()  # Empty queryset for now

    # Collect user profiles updated
    profiles_updated = Profile.objects.filter(
        updated__gte=from_timestamp,
        updated__lte=until_timestamp
    )

    # Collect new user content created
    user_content_created = UserContent.objects.filter(
        created__gte=from_timestamp,
        created__lte=until_timestamp
    )

    # Collect user content updated (excluding newly created ones)
    user_content_updated = UserContent.objects.filter(
        Q(updated__gte=from_timestamp) & Q(updated__lte=until_timestamp) &
        ~Q(created__gte=from_timestamp)
    )

    # Collect new user enrolments created
    user_enrolments_created = UserEnrolment.objects.filter(
        created__gte=from_timestamp,
        created__lte=until_timestamp
    )

    # Collect user enrolments updated (excluding newly created ones)
    user_enrolments_updated = UserEnrolment.objects.filter(
        Q(updated__gte=from_timestamp) & Q(updated__lte=until_timestamp) &
        ~Q(created__gte=from_timestamp)
    )

    # Collect new session user content created
    session_user_content_created = SessionUserContent.objects.filter(
        created__gte=from_timestamp,
        created__lte=until_timestamp
    )

    # Collect session user content updated (excluding newly created ones)
    session_user_content_updated = SessionUserContent.objects.filter(
        Q(updated__gte=from_timestamp) & Q(updated__lte=until_timestamp) &
        ~Q(created__gte=from_timestamp)
    )

    return {
        'timestamp_range': {
            'from_timestamp': from_timestamp.strftime('%Y%m%d_%H%M%S'),
            'until_timestamp': until_timestamp.strftime('%Y%m%d_%H%M%S'),
        },
        'users_created': [serialize_user(user) for user in users_created],
        'users_updated': [serialize_user(user) for user in users_updated],
        'profiles_updated': [serialize_profile(profile) for profile in profiles_updated],
        'user_content_created': [serialize_user_content(content) for content in user_content_created],
        'user_content_updated': [serialize_user_content(content) for content in user_content_updated],
        'user_enrolments_created': [serialize_user_enrolment(enrolment) for enrolment in user_enrolments_created],
        'user_enrolments_updated': [serialize_user_enrolment(enrolment) for enrolment in user_enrolments_updated],
        'session_user_content_created': [serialize_session_user_content(content) for content in session_user_content_created],
        'session_user_content_updated': [serialize_session_user_content(content) for content in session_user_content_updated],
    }
