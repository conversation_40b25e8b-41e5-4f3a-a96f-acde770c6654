from datetime import datetime, timezone
from django.contrib.auth import get_user_model
from django.db import transaction
from uuid import UUID

from apps.users.models import Profile
from apps.user_content.models import UserContent, UserEnrolment, SessionUserContent


User = get_user_model()


def parse_datetime(datetime_str):
    """Parse ISO format datetime string to datetime object"""
    if not datetime_str:
        return None
    try:
        return datetime.fromisoformat(datetime_str.replace('Z', '+00:00'))
    except ValueError:
        # Try parsing without timezone info and add UTC
        try:
            dt = datetime.fromisoformat(datetime_str)
            if dt.tzinfo is None:
                dt = dt.replace(tzinfo=timezone.utc)
            return dt
        except ValueError:
            raise ValueError(f"Invalid datetime format: {datetime_str}")


def process_sync_data(sync_data):
    """
    Process synchronization data and create/update objects accordingly.
    
    Args:
        sync_data (dict): Dictionary containing the sync data with keys:
            - users_created
            - users_updated
            - profiles_updated
            - user_content_created
            - user_content_updated
            - user_enrolments_created
            - user_enrolments_updated
            - session_user_content_created
            - session_user_content_updated
    
    Returns:
        dict: Summary of processed items
    """
    
    results = {
        'users_created': 0,
        'users_updated': 0,
        'profiles_updated': 0,
        'user_content_created': 0,
        'user_content_updated': 0,
        'user_enrolments_created': 0,
        'user_enrolments_updated': 0,
        'session_user_content_created': 0,
        'session_user_content_updated': 0,
        'errors': []
    }
    
    with transaction.atomic():
        try:
            # Process new users
            for user_data in sync_data.get('users_created', []):
                try:
                    user_id = UUID(user_data['id'])
                    
                    # Check if user already exists
                    if not User.objects.filter(id=user_id).exists():
                        user = User.objects.create(
                            id=user_id,
                            email=user_data['email'],
                            first_name=user_data['first_name'],
                            last_name=user_data['last_name'],
                            created=parse_datetime(user_data['created']),
                            is_staff=user_data.get('is_staff', False),
                            is_admin=user_data.get('is_admin', False),
                            chargebee_id=user_data.get('chargebee_id'),
                            subscription_id=user_data.get('subscription_id'),
                            plan_id=user_data.get('plan_id'),
                            is_active=user_data.get('is_active', True),
                            is_verified=user_data.get('is_verified', False),
                            activation_token=user_data.get('activation_token'),
                            password_reset_token=user_data.get('password_reset_token'),
                            community_user_id=user_data.get('community_user_id'),
                            community_user_username=user_data.get('community_user_username'),
                            community_groups=user_data.get('community_groups', []),
                            hubspot_id=user_data.get('hubspot_id'),
                            subscribed_to_newsletter=user_data.get('subscribed_to_newsletter', False),
                        )
                        results['users_created'] += 1
                except Exception as e:
                    results['errors'].append(f"Error creating user {user_data.get('id')}: {str(e)}")
            
            # Process user updates
            for user_data in sync_data.get('users_updated', []):
                try:
                    user_id = UUID(user_data['id'])
                    user = User.objects.filter(id=user_id).first()
                    
                    if user:
                        # Update user fields
                        user.email = user_data['email']
                        user.first_name = user_data['first_name']
                        user.last_name = user_data['last_name']
                        user.is_staff = user_data.get('is_staff', False)
                        user.is_admin = user_data.get('is_admin', False)
                        user.chargebee_id = user_data.get('chargebee_id')
                        user.subscription_id = user_data.get('subscription_id')
                        user.plan_id = user_data.get('plan_id')
                        user.is_active = user_data.get('is_active', True)
                        user.is_verified = user_data.get('is_verified', False)
                        user.activation_token = user_data.get('activation_token')
                        user.password_reset_token = user_data.get('password_reset_token')
                        user.community_user_id = user_data.get('community_user_id')
                        user.community_user_username = user_data.get('community_user_username')
                        user.community_groups = user_data.get('community_groups', [])
                        user.hubspot_id = user_data.get('hubspot_id')
                        user.subscribed_to_newsletter = user_data.get('subscribed_to_newsletter', False)
                        user.save()
                        results['users_updated'] += 1
                except Exception as e:
                    results['errors'].append(f"Error updating user {user_data.get('id')}: {str(e)}")
            
            # Process profile updates
            for profile_data in sync_data.get('profiles_updated', []):
                try:
                    user_id = UUID(profile_data['user_id'])
                    user = User.objects.filter(id=user_id).first()
                    
                    if user:
                        profile, created = Profile.objects.get_or_create(user=user)
                        
                        # Update profile fields
                        profile.privacy_rules = profile_data.get('privacy_rules', {})
                        profile.private_details = profile_data.get('private_details', {})
                        profile.member_details = profile_data.get('member_details', {})
                        profile.friends_details = profile_data.get('friends_details', {})
                        profile.public_details = profile_data.get('public_details', {})
                        profile.interests = profile_data.get('interests', {})
                        profile.newsletters = profile_data.get('newsletters', {})
                        profile.social_networks = profile_data.get('social_networks', {})
                        profile.save()
                        results['profiles_updated'] += 1
                except Exception as e:
                    results['errors'].append(f"Error updating profile for user {profile_data.get('user_id')}: {str(e)}")
            
            # Process new user content
            for content_data in sync_data.get('user_content_created', []):
                try:
                    content_id = UUID(content_data['id'])
                    user_id = UUID(content_data['user_id'])
                    
                    # Check if content already exists
                    if not UserContent.objects.filter(id=content_id).exists():
                        user = User.objects.filter(id=user_id).first()
                        if user:
                            content = UserContent.objects.create(
                                id=content_id,
                                user=user,
                                created=parse_datetime(content_data['created']),
                                updated=parse_datetime(content_data['updated']),
                                content_type=content_data['content_type'],
                                slug=content_data['slug'],
                                index_id=content_data['index_id'],
                                description=content_data.get('description'),
                                chargebee_entity_id=content_data.get('chargebee_entity_id'),
                                object_data=content_data.get('object_data'),
                                bookmarked=content_data.get('bookmarked', False),
                                completed=content_data.get('completed', False),
                                on_demand=content_data.get('on_demand', True),
                                is_active=content_data.get('is_active', True),
                            )
                            results['user_content_created'] += 1
                except Exception as e:
                    results['errors'].append(f"Error creating user content {content_data.get('id')}: {str(e)}")
            
            # Process user content updates
            for content_data in sync_data.get('user_content_updated', []):
                try:
                    content_id = UUID(content_data['id'])
                    content = UserContent.objects.filter(id=content_id).first()
                    
                    if content:
                        content.content_type = content_data['content_type']
                        content.slug = content_data['slug']
                        content.index_id = content_data['index_id']
                        content.description = content_data.get('description')
                        content.chargebee_entity_id = content_data.get('chargebee_entity_id')
                        content.object_data = content_data.get('object_data')
                        content.bookmarked = content_data.get('bookmarked', False)
                        content.completed = content_data.get('completed', False)
                        content.on_demand = content_data.get('on_demand', True)
                        content.is_active = content_data.get('is_active', True)
                        content.save()
                        results['user_content_updated'] += 1
                except Exception as e:
                    results['errors'].append(f"Error updating user content {content_data.get('id')}: {str(e)}")
            
            # Process new user enrolments
            for enrolment_data in sync_data.get('user_enrolments_created', []):
                try:
                    enrolment_id = UUID(enrolment_data['id'])
                    user_id = UUID(enrolment_data['user_id'])
                    content_id = UUID(enrolment_data['content_id'])
                    
                    # Check if enrolment already exists
                    if not UserEnrolment.objects.filter(id=enrolment_id).exists():
                        user = User.objects.filter(id=user_id).first()
                        content = UserContent.objects.filter(id=content_id).first()
                        
                        if user and content:
                            enrolment = UserEnrolment.objects.create(
                                id=enrolment_id,
                                user=user,
                                content=content,
                                created=parse_datetime(enrolment_data['created']),
                                updated=parse_datetime(enrolment_data['updated']),
                                is_active=enrolment_data.get('is_active', True),
                                completed_sessions=enrolment_data.get('completed_sessions', []),
                                progress=enrolment_data.get('progress', '0'),
                                is_completed=enrolment_data.get('is_completed', False),
                                completed_on=parse_datetime(enrolment_data.get('completed_on')),
                            )
                            results['user_enrolments_created'] += 1
                except Exception as e:
                    results['errors'].append(f"Error creating user enrolment {enrolment_data.get('id')}: {str(e)}")
            
            # Process user enrolment updates
            for enrolment_data in sync_data.get('user_enrolments_updated', []):
                try:
                    enrolment_id = UUID(enrolment_data['id'])
                    enrolment = UserEnrolment.objects.filter(id=enrolment_id).first()
                    
                    if enrolment:
                        enrolment.is_active = enrolment_data.get('is_active', True)
                        enrolment.completed_sessions = enrolment_data.get('completed_sessions', [])
                        enrolment.progress = enrolment_data.get('progress', '0')
                        enrolment.is_completed = enrolment_data.get('is_completed', False)
                        enrolment.completed_on = parse_datetime(enrolment_data.get('completed_on'))
                        enrolment.save()
                        results['user_enrolments_updated'] += 1
                except Exception as e:
                    results['errors'].append(f"Error updating user enrolment {enrolment_data.get('id')}: {str(e)}")
            
            # Process new session user content
            for session_data in sync_data.get('session_user_content_created', []):
                try:
                    session_id = UUID(session_data['id'])
                    user_id = UUID(session_data['user_id'])
                    
                    # Check if session content already exists
                    if not SessionUserContent.objects.filter(id=session_id).exists():
                        user = User.objects.filter(id=user_id).first()
                        if user:
                            session_content = SessionUserContent.objects.create(
                                id=session_id,
                                user=user,
                                created=parse_datetime(session_data['created']),
                                updated=parse_datetime(session_data['updated']),
                                content_type=session_data['content_type'],
                                slug=session_data['slug'],
                                index_id=session_data['index_id'],
                                description=session_data.get('description'),
                                object_data=session_data.get('object_data'),
                                bookmarked=session_data.get('bookmarked', False),
                                completed=session_data.get('completed', False),
                            )
                            results['session_user_content_created'] += 1
                except Exception as e:
                    results['errors'].append(f"Error creating session user content {session_data.get('id')}: {str(e)}")
            
            # Process session user content updates
            for session_data in sync_data.get('session_user_content_updated', []):
                try:
                    session_id = UUID(session_data['id'])
                    session_content = SessionUserContent.objects.filter(id=session_id).first()
                    
                    if session_content:
                        session_content.content_type = session_data['content_type']
                        session_content.slug = session_data['slug']
                        session_content.index_id = session_data['index_id']
                        session_content.description = session_data.get('description')
                        session_content.object_data = session_data.get('object_data')
                        session_content.bookmarked = session_data.get('bookmarked', False)
                        session_content.completed = session_data.get('completed', False)
                        session_content.save()
                        results['session_user_content_updated'] += 1
                except Exception as e:
                    results['errors'].append(f"Error updating session user content {session_data.get('id')}: {str(e)}")
        
        except Exception as e:
            results['errors'].append(f"Transaction error: {str(e)}")
            raise
    
    return results
