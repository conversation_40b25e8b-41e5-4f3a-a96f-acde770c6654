from config import settings

from apps.user_content.models import (
    UserContent,
    UserEnrolment,
)
from apps.user_content.serializers import EnrolmentSerializer

from utils.api.products import get_minimum_product_data

from api.events.models import event_sessions
from api.courses.models import course_sessions

from utils.api.products import get_session_user_content
from utils.hubspot.users import (
    map_user_latest_purchase as hubspot_map_user_latest_purchase,
    update_user_details as hubspot_update_user_details,
    subscribe_user as hubspot_subscribe_user,
)



SESSION_CONTENT_TYPE_MAPPER = {
    'events': event_sessions.get_indexed_id(),
    'courses': course_sessions.get_indexed_id(),
}
SESSION_MODEL_SERIES_MAPPER = {
    'events': event_sessions,
    'courses': course_sessions,
}


def enrol_user(product, content_type, user, serializer=EnrolmentSerializer,
               strip_product=True, subscribe=True):
    user_content, content_created = UserContent.objects.get_or_create(
        user=user,
        index_id=product.get('id'),
        slug=product.get('slug'),
        content_type=content_type,
    )
    if content_created:
        user_content.chargebee_entity_id = product.get('chargebee_entity_id')
        if strip_product:
            user_content.object_data = get_minimum_product_data(product)
        else:
            user_content.object_data = product
        # user_content.bookmarked = True
    user_content.is_active = True
    user_content.save()

    hubspot_data = {}
    if content_type == 'events':
        hubspot_data = hubspot_map_user_latest_purchase(
            product, 'latest_event_registration')
    elif user.plan_id in (settings.CHARGEBEE_THRIVE_MONTHLY_PLAN_IDS +
                          settings.CHARGEBEE_THRIVE_YEARLY_PLAN_IDS):
        hubspot_data = hubspot_map_user_latest_purchase(
                user_content.object_data)
    if hubspot_data.keys():
        hubspot_update_user_details(user, hubspot_data)

    # automatically joins group in Community and updates permissions
    user.community_groups.append(product.get('community_group_name'))
    user.save()

    enrolment, enrolment_created = UserEnrolment.objects.get_or_create(
        user=user,
        content=user_content,
        # user_id=user.id,
        # content_id=user_content.id,
    )
    if enrolment_created and subscribe and \
            product.get('hubspot_list_id', False):
        hubspot_subscribe_user(
            user_content.user, product.get('hubspot_list_id'))

    return enrolment


def is_user_enrolled(user, content_type, slug):
    return UserEnrolment.objects.select_related("content").filter(
        user=user,
        content__content_type=content_type,
        content__slug=slug).exists()


def get_user_enrolment(user, content_type, slug):
    return UserEnrolment.objects.select_related("content").get(
        user=user, content__content_type=content_type, content__slug=slug)


def update_session_user_content(enrolment, slug):
    content_type = SESSION_CONTENT_TYPE_MAPPER.get(
        enrolment.content.content_type)
    session = SESSION_MODEL_SERIES_MAPPER.get(
        enrolment.content.content_type).get(slug)
    user_content_session = get_session_user_content(
        session, content_type, enrolment.user)
    user_content_session.completed = True
    user_content_session.save()


def update_progress(enrolment, new_progress):
    if new_progress not in enrolment.completed_sessions:
        enrolment.completed_sessions.append(new_progress)
        enrolment.save()
        update_session_user_content(enrolment, new_progress)
        enrolment.update_progress()

    return enrolment
