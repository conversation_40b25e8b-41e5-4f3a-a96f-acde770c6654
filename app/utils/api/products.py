from utils.chargebee.users import (
    generate_single_purchase_hosted_page,
    get_chargebee_product_price,
    handle_abandoned_product_cart,
)

from apps.authentication.permissions import (
    check_course_permissions,
    check_event_permissions,
    check_media_permissions,
)
from apps.user_content.models import (
    UserContent,
    SessionUserContent,
)
from apps.user_content.serializers import (
    UserContentSerializer,
    SessionUserContentSerializer,
)
from utils.api.serializers import EventSessionPublicSerializer



product_permissions_checker = {
    'courses': check_course_permissions,
    'events': check_event_permissions,
    'films': check_media_permissions,
    'podcasts': check_media_permissions,
    'articles': check_media_permissions,
    'webinars': check_media_permissions,
    'entities': lambda a, b, c: True,
    'categories': lambda a, b, c: True,
}

minimum_public_properties = ['_meilisearch_id',
                             'title',
                             'description',
                             'thumbnail',
                             'picture',
                             'teachers',
                             'dateHeadline',
                             'startDateTime',
                             'endDateTime',
                             'date',
                             'isDarkBackground',
                             'seo',
                             'slug']

MINIMUM_PRODUCT_KEYS = [
    'id',
    'slug',
    'startDateTime',
    'endDateTime',
    'date',
    'isDarkBackground',
    'picture',
    'thumbnail',
    'title',
    'description',
    'chargebee_entity_id',
    'hubspot_list_id',
    'community_category_slug',
    'community_group_name',
]



def get_minimum_product_data(product):
    clean_product = dict({key: product.get(key) for key in MINIMUM_PRODUCT_KEYS
                          if product.get(key) is not None})
    clean_product['teachers'] = [teacher.get('name') for
                                 teacher in product.get('teachers')]
    return clean_product


def handle_product_purchase(product, content_type, user):
    product['content_type'] = content_type
    hosted_page = generate_single_purchase_hosted_page(product, user)

    user_content_data = {
        'user': user,
        'content_type': content_type,
        'slug': product.get('slug'),
        'index_id': product.get('id'),
        'chargebee_entity_id': product.get('chargebee_entity_id'),
    }
    user_content, created = UserContent.objects.get_or_create(**user_content_data)

    if created:
        user_content.on_demand = True
        user_content.is_active = False
        user_content.object_data = get_minimum_product_data(product)
        user_content.save()
        handle_abandoned_product_cart(user, product)

    return hosted_page


def generate_gift_purchase(chargebee_entity_id, user):
    price = get_chargebee_product_price(chargebee_entity_id)
    product_obj = {
        "chargebee_entity_id": chargebee_entity_id,
        "price": price,
    }
    hosted_page = generate_single_purchase_hosted_page(
        product_obj, user, price_percent=True)

    return hosted_page


def bookmark_content(
        product, content_type, user, serializer=UserContentSerializer):
    """
    Bookmards a product of a given content type.

    :param product: Meilisearch response object
    :param content_type: 'courses', 'events', 'films', 'podcasts' or 'articles'
    :param user: User instance
    :param serializer: serializer class to be used, UserContentSerializer by default
    :return: serialized UserContent instance
    """
    user_content, created = UserContent.objects.get_or_create(
        user=user,
        content_type=content_type,
        index_id=product.get('id'),
        slug=product.get('slug'),
    )
    if created:
        user_content.object_data = get_minimum_product_data(product)
        user_content.chargebee_entity_id = product.get('chargebee_entity_id')
    elif user_content.bookmarked:
        user_content.bookmarked = False
        user_content.save()
        return serializer(user_content).data

    # should wait for the purchase to come through on Chargebee webhook
    validate_permissions = product_permissions_checker[content_type]
    if not validate_permissions(user, product, content_type):
        user_content.is_active = False

    user_content.bookmarked = True
    user_content.save()

    return serializer(user_content).data


def get_session_user_content(session, content_type, user):
    user_content, created = SessionUserContent.objects.get_or_create(
        user=user,
        content_type=content_type,
        index_id=session.get('id'),
        slug=session.get('slug'),
    )
    if created:
        user_content.object_data = EventSessionPublicSerializer(session).data
        user_content.description = session.get('title')
        user_content.save()

    return user_content


def bookmark_session_content(
    session, content_type, user, serializer=SessionUserContentSerializer):
    user_content = get_session_user_content(session, content_type, user)
    if user_content.bookmarked:
        user_content.bookmarked = False
        user_content.save()
        return serializer(user_content).data

    user_content.bookmarked = True
    user_content.save()

    return serializer(user_content).data


content_type_bookmark_handler = {
    'courses': bookmark_content,
    'events': bookmark_content,
    'films': bookmark_content,
    'podcasts': bookmark_content,
    'articles': bookmark_content,
    'entities': bookmark_content,
    'categories': bookmark_content,
    'course-session': bookmark_session_content,
    'event-session': bookmark_session_content,
}


def is_user_purchase(user, content_type, slug):
    return user.content.filter(
        content_type=content_type,
        slug=slug,
        on_demand=True,
        is_active=True).exists()


def clean_product_for_seo(content):
    clean_content = {}

    for key in minimum_public_properties:
        clean_content[key] = content.get(key, None)

    return clean_content


def clean_products_for_seo(results):
    return [clean_product_for_seo(result) for result in results]
