from rest_framework import serializers


class ContentResultsSerializer:
    """
    Serializer for content results.
    """
    def __init__(self, data, sessions_ids=None):
        self._raw_data = data
        self.results = data.get('hits', [])
        self.total = data.get('estimatedTotalHits', 0)
        self.limit = data.get('limit', 20)
        self.offset = data.get('offset', 0)
        self.query = data.get('query', '')

        if sessions_ids:
            # Sort the results based on the order of sessions_ids
            self.results = sorted(
                self.results, key=lambda x: sessions_ids.index(x['slug']))

    def to_json(self):
        return {
            'results': self.results,
            'total': self.total,
            'limit': self.limit,
            'offset': self.offset,
            'query': self.query,
        }


class EventSessionPublicSerializer(serializers.Serializer):
    """
    Serializer for public event sessions.
    """
    def to_representation(self, instance):
        return {
            'id': instance.get('id'),
            'title': instance.get('title'),
            'description': instance.get('description'),
            'dateHeadline': instance.get('dateHeadline'),
            'headline': instance.get('headline'),
            'startDateTime': instance.get('startDateTime'),
            'endDateTime': instance.get('endDateTime'),
            'slug': instance.get('slug'),
            'seriesSlug': instance.get('seriesSlug'),
            'isDarkBackground': instance.get('isDarkBackground'),
            'thumbnail': instance.get('thumbnail'),
            'picture': instance.get('picture'),
            'teachers': instance.get('teachersList', {}).get('list'),
            'contentType': instance.get('contentType'),
            'categories': instance.get('categories'),
            'seo': instance.get('seo'),
        }
