from itertools import chain
from datetime import datetime

from django.contrib.auth import get_user_model

from utils.meilisearch.router import (
    content_type_index_mapper as index_mapper
)

from apps.user_content.models import (
    UserContent,
    UserContentPending,
)
from apps.user_content.serializers import UserContentSerializer

from utils.api.enrolment import enrol_user
from utils.api.products import get_minimum_product_data



User = get_user_model()


def get_courses(user):
    return UserContentSerializer(
        UserContent.courses_objects.filter(
            user=user).order_by('-updated'), many=True).data

def get_events(user):
    return UserContentSerializer(
        UserContent.events_objects.filter(
            user=user).order_by('-updated'), many=True).data

relative_content = {
    'courses-landing': get_courses,
    'events-landing': get_events,
}


def include_user_content(content, user):
    # complement page request with user content
    if (user and user.is_authenticated and
            content['slug'] in relative_content.keys()):
            content['user-content'] = relative_content[content['slug']](user)
    return content

def process_existing_content(content_type, object_data, user):
    sessions = object_data.pop('sessions', [])
    # make sure content isn't created more than once
    content, _ = UserContent.objects.get_or_create(**{
        'user': user,
        'content_type': content_type,
        'slug': object_data.get('slug'),
        'index_id': object_data.get('id'),
    })
    content.description = object_data.get('title')
    content.chargebee_entity_id = object_data.get('chargebee_entity_id')
    content.object_data = object_data
    content.on_demand = True
    content.is_active = True
    content.save()

    if content_type in ['courses', 'events']:
        enrol_user(object_data, content_type, user, subscribe=False)
        # if object data doesn't contain endDateTime, skips session completion
        if object_data.get('endDateTime', False):
            content_datetime = datetime.strptime(
                object_data.get('endDateTime'), '%Y-%m-%dT%H:%M:%S.%fZ')
            # complete sessions if content has past date
            if content_datetime < datetime.now():
                content.enrolment.completed_sessions = sessions
                content.enrolment.is_completed = True
                content.enrolment.save()
                content.enrolment.update_progress()


def create_past_participants(content_type, content_slug, users_emails):
    product = index_mapper[content_type].get(content_slug)
    object_data = get_minimum_product_data(product)
    sessions_list = list(chain(
        *[[s.get('slug') for s in m.get('sessions', {})] for
          m in product.get('modules', {})]))
    object_data['sessions'] = list(filter(
        lambda s: s is not None, sessions_list))
    if product:
        email_list = list(map(lambda e: e.strip(), users_emails.lower().split(',')))
        bulk_user_content = []
        existing_users = User.objects.prefetch_related('content').filter(
            email__in=email_list)
        if existing_users.exists():
            existing_users_emails = existing_users.values_list(
                'email', flat=True).distinct()
            email_list = list(filter(
                lambda e: e not in existing_users_emails, email_list))
            for user in existing_users:
                existing_content = user.content.filter(
                    slug=content_slug, content_type=content_type).first()
                if not existing_content:
                    bulk_user_content.append(UserContentPending(**{
                        'email': user.email,
                        'content_type': content_type,
                        'slug': content_slug,
                        'index_id': product.get('id'),
                        'description': product.get('title'),
                        'chargebee_entity_id': product.get('chargebee_entity_id'),
                        'object_data': object_data,
                        'is_active': False,
                    }))
                    process_existing_content(content_type, object_data, user)
                elif not existing_content.is_active:
                    # activate existing user content
                    existing_content.is_active = True
                    existing_content.on_demand = True
                    existing_content.save()
                elif (existing_content.is_active and
                      not getattr(existing_content, 'enrolment', False)):
                    process_existing_content(content_type, object_data, user)
                else:
                    print(f"Import pending content: {content_type} {content_slug}"
                          f" is already active for {user.email}")

        for email in email_list:
            bulk_user_content.append(UserContentPending(**{
                'email': email,
                'content_type': content_type,
                'slug': content_slug,
                'index_id': product.get('id'),
                'description': product.get('title'),
                'chargebee_entity_id': product.get('chargebee_entity_id'),
                'object_data': object_data,
            }))
        UserContentPending.objects.bulk_create(bulk_user_content)

def process_pending_content(user):
    products_on_hold = UserContentPending.objects.filter(
        email=user.email, is_active=True)
    for content in products_on_hold:
        process_existing_content(
            content.content_type, content.object_data, user)
        content.is_active = False
        content.save()
