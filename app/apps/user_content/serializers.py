from rest_framework import serializers

from django.contrib.auth import get_user_model

from apps.user_content.models import (
    UserContent,
    UserEnrolment,
    SessionUserContent,
)
from apps.users.serializers import (
    UserSerializer,
    PrivateProfileSerializer,
)

from utils.nodebb.auth import encode_user_token as nodebb_encode_user_token



User = get_user_model()


flatten_content = lambda content_list: {
    key: value for dictionary in content_list for
    key, value in dictionary.items()
}


class UserContentSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserContent
        read_only_fields = ('created', 'updated')
        fields = ('id',
                  'content_type',
                  'index_id',
                  'slug',
                  'object_data',
                  'user_data',
                  'is_active',
                  'bookmarked',
                  'created',
                  'updated')

    def to_representation(self, instance):
        return {
            'id': instance.id,
            'contentType': instance.content_type,
            'indexId': instance.index_id,
            'slug': instance.slug,
            'objectData': instance.object_data,
            'userData': instance.user_data,
            'isActive': instance.is_active,
            'isFavourite': instance.bookmarked,
            'created': instance.created,
            'updated': instance.updated
        }


class SimpleUserContentSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserContent
        read_only_fields = ('created', 'updated')
        fields = ('id',
                  'content_type',
                  'index_id',
                  'slug',
                  'is_active',
                  'bookmarked',
                  'created',
                  'updated')

    def to_representation(self, instance):
        return {
            'id': instance.id,
            'contentType': instance.content_type,
            'indexId': instance.index_id,
            'slug': instance.slug,
            'isActive': instance.is_active,
            'isFavourite': instance.bookmarked,
            'created': instance.created,
            'updated': instance.updated
        }


class SessionUserContentSerializer(serializers.ModelSerializer):
    class Meta:
        model = SessionUserContent
        fields = ('slug',)

    def to_representation(self, instance):
        return {
            'id': instance.id,
            'contentType': instance.content_type,
            'slug': instance.slug,
            'isFavourite': instance.bookmarked,
            **instance.object_data,
        }


class SlugUserContentSerializer(serializers.ModelSerializer):
    progress = serializers.SerializerMethodField()
    sessions = serializers.SerializerMethodField()
    enrolment = serializers.SerializerMethodField()

    class Meta:
        model = UserContent
        fields = ('slug',
                  'progress',
                  'sessions',
                  'enrolment')
                  # 'object_data',
                  # 'on_demand',
                  # 'is_active',
                  # 'bookmarked',
                  # 'completed')

    def get_progress(self, instance):
        enrolment = getattr(instance, 'enrolment', None)
        return enrolment.progress if enrolment else 0

    def get_sessions(self, instance):
        enrolment = getattr(instance, 'enrolment', None)
        return enrolment.completed_sessions if enrolment else []

    def get_enrolment(self, instance):
        enrolment = getattr(instance, 'enrolment', False)
        return bool(enrolment)

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        # TODO: keep only progress, sessions, isPurchased and slug in the future
        # TODO: create separate serializer for /profile/:userId/:contentType
        return {
            representation['slug']: {
                'progress': representation.pop('progress'),
                'sessions': representation.pop('sessions'),
                'isRegistered': representation.pop('enrolment'),
                'contentType': instance.content_type,
                'isPurchased': instance.on_demand,
                'isActive': instance.is_active,
                'isFavourite': instance.bookmarked,
                **representation,
                **instance.object_data,
            }
        }


class SlugSessionUserContentSerializer(serializers.ModelSerializer):
    class Meta:
        model = SessionUserContent
        fields = ('slug',)

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        return {
            representation['slug']: {
                'contentType': instance.content_type,
                'isFavourite': instance.bookmarked,
                **representation,
                **instance.object_data,
            }
        }



class UserContentResultsSerializer(serializers.ModelSerializer):
    progress = serializers.SerializerMethodField()
    sessions = serializers.SerializerMethodField()
    enrolment = serializers.SerializerMethodField()

    class Meta:
        model = UserContent
        fields = ('slug',
                  'progress',
                  'sessions',
                  'enrolment')
                  # 'content_type',
                  # 'object_data',
                  # 'on_demand',
                  # 'is_active',
                  # 'bookmarked',
                  # 'completed')

    def get_progress(self, instance):
        enrolment = getattr(instance, 'enrolment', None)
        return enrolment.progress if enrolment else 0

    def get_sessions(self, instance):
        enrolment = getattr(instance, 'enrolment', None)
        return enrolment.completed_sessions if enrolment else []

    def get_enrolment(self, instance):
        enrolment = getattr(instance, 'enrolment', False)
        return bool(enrolment)

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        return {
            'progress': representation.pop('progress'),
            'sessions': representation.pop('sessions'),
            'isRegistered': representation.pop('enrolment'),
            'isPurchased': instance.on_demand,
            'isActive': instance.is_active,
            'isFavourite': instance.bookmarked,
            'contentType': instance.content_type,
            **representation,
            **instance.object_data,
        }

    #
    # def to_representation(self, instance):
    #     return {
    #         'progress': self.get_progress(instance),
    #         'sessions': self.get_sessions(instance),
    #         'isPurchased': instance.on_demand,
    #         'isActive': instance.is_active,
    #         'isFavourite': instance.bookmarked,
    #         # 'completed': self.completed,
    #         **instance.object_data,
    #     }


class SessionUserContentResultsSerializer(serializers.ModelSerializer):
    class Meta:
        model = SessionUserContent
        fields = ('slug',)
                  # 'content_type',
                  # 'object_data',
                  # 'on_demand',
                  # 'is_active',
                  # 'bookmarked',
                  # 'completed')

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        return {
            'isFavourite': instance.bookmarked,
            'contentType': instance.content_type,
            **representation,
            **instance.object_data,
        }


class StaffUserContentSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)

    class Meta:
        model = UserContent
        read_only_fields = ('created', 'updated')
        fields = ('id',
                  'content_type',
                  'index_id',
                  'slug',
                  # 'object_data',
                  'user_data',
                  'user',
                  'bookmarked',
                  'is_active',
                  'created',
                  'updated')

    def to_representation(self, instance):
        return {
            'id': instance.id,
            'contentType': instance.content_type,
            'indexId': instance.index_id,
            'slug': instance.slug,
            'userData': instance.user_data,
            'user': instance.user,
            'isActive': instance.is_active,
            'bookmarked': instance.bookmarked,
            'created': instance.created,
            'updated': instance.updated
        }


class CurrentUserSerializer(serializers.ModelSerializer):
    favourites = serializers.SerializerMethodField()
    # profile = PrivateProfileSerializer(source='profile', read_only=True)
    products = serializers.SerializerMethodField()
    profile = serializers.SerializerMethodField()
    community = serializers.SerializerMethodField()
    community_username = serializers.SerializerMethodField()
    has_billing = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ('id',
                  'email',
                  'first_name',
                  'last_name'
                  'is_verified',
                  'plan_id',
                  'subscribed_to_newsletter',
                  'favourites',
                  'profile',
                  'has_billing',
                  'community_username',)

    def get_favourites(self, instance):
        # slug values of all the favourites
        user_content = UserContent.bookmarked_objects.filter(user=instance).values_list(
            'slug', flat=True).distinct()
        user_sessions_content = SessionUserContent.bookmarked_objects.filter(
            user=instance).values_list('slug', flat=True).distinct()
        return list(user_content) + list(user_sessions_content)

    def get_products(self, instance):
        user_content = instance.content.select_related("enrolment").all()
        event_series_content = user_content.filter(
            content_type='events', enrolment__isnull=False, is_active=True)

        courses = SlugUserContentSerializer(user_content.filter(
            content_type='courses', enrolment__isnull=False, is_active=True), many=True).data
        event_series = SlugUserContentSerializer(
            event_series_content, many=True).data
        event_sessions = SlugSessionUserContentSerializer(
            SessionUserContent.events_objects.filter(
                user=instance,
                slug__in=event_series_content.values_list('slug', flat=True)
            ), many=True).data
        films = SlugUserContentSerializer(user_content.filter(
            content_type='films', on_demand=True, is_active=True),
            many=True).data
        podcasts = SlugUserContentSerializer(user_content.filter(
            content_type='podcasts', on_demand=True, is_active=True),
            many=True).data
        articles = SlugUserContentSerializer(user_content.filter(
            content_type='articles', on_demand=True, is_active=True),
            many=True).data
        webinars = SlugUserContentSerializer(user_content.filter(
            content_type='webinars', is_active=True),
            many=True).data

        return {
            "courses": flatten_content(courses),
            "events": {
                **flatten_content(event_series),
                **flatten_content(event_sessions),
            },
            "films": flatten_content(films),
            "podcasts": flatten_content(podcasts),
            "articles": flatten_content(articles),
            "webinars": flatten_content(webinars),
        }

    def get_profile(self, instance):
        return PrivateProfileSerializer(instance.profile).data if \
            hasattr(instance, 'profile') else {}

    def get_community(self, instance):
        return nodebb_encode_user_token(instance) if \
            instance.community_user_id else {}

    def get_community_username(self, instance):
        return instance.community_user_username if \
            instance.community_user_id else ""

    def get_has_billing(self, instance):
        return bool(instance.chargebee_id)

    def to_representation(self, instance):
        return {
            'id': instance.id,
            'email': instance.email,
            'first_name': instance.first_name,
            'last_name': instance.last_name,
            'is_verified': instance.is_verified,
            'plan_id': instance.plan_id,
            'subscribedToNewsletter': instance.subscribed_to_newsletter,
            'products': self.get_products(instance),
            'favourites': self.get_favourites(instance),
            'profile': self.get_profile(instance),
            'community': self.get_community(instance),
            'communityUsername': self.get_community_username(instance),
            'hasBilling': self.get_has_billing(instance),
        }


class EnrolmentSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    content = SimpleUserContentSerializer(read_only=True)

    class Meta:
        model = UserEnrolment
        read_only_fields = ('completed_sessions',
                            'is_active',
                            'is_completed',
                            'completed_on',
                            'progress',
                            'content')
        fields = ('id',
                  'user',
                  'content',
                  'is_active',
                  'completed_sessions',
                  'is_completed',
                  'completed_on',
                  'progress')

    def to_representation(self, instance):
        return {
            'id': instance.id,
            'user': instance.user,
            'content': instance.content,
            'isActive': instance.is_active,
            'completedSessions': instance.completed_sessions,
            'isCompleted': instance.is_completed,
            'completedOn': instance.completed_on,
            'progress': instance.progress
        }
