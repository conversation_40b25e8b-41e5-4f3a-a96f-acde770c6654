from django.contrib import admin
from django.contrib.auth import get_user_model
from django.core.exceptions import ObjectDoesNotExist

from .models import (
    UserContent,
    UserContentPending,
    UserEnrolment,
)
from utils.api.user_content import process_existing_content

from utils.api.enrolment import enrol_user



User = get_user_model()


@admin.action(description="Activate content and enrol user")
def content_activate_and_enrol(modeladmin, request, queryset):
    for content in queryset:
        content.is_active = True
        content.save()

        if content.content_type in ['courses', 'events']:
            enrol_user(
                content.object_data,
                content.content_type,
                content.user,
                strip_product=False)

        success_message = f"User content updated for " \
                          f"{content.user.chargebee_id}:{content.user.email} " \
                          f"[{content.content_type}][{content.chargebee_entity_id}]"
        print(success_message)


class UserContentAdmin(admin.ModelAdmin):
    list_per_page = 20

    def get_queryset(self, request):
        # qs = super().get_queryset(request)
        return UserContent.objects.select_related("user").all()

    list_display = ("content_title",
                    "slug",
                    "content_type",
                    "is_active",
                    "on_demand",
                    "bookmarked",
                    "completed",
                    "chargebee_entity_id",)
    list_filter = ("user__plan_id",
                   "content_type",
                   "is_active",
                   "on_demand",
                   "bookmarked",
                   "completed",
                   "created",
                   "updated",)
    search_fields = ("user__email",
                     "slug",
                     "description",
                     "chargebee_entity_id",)
    date_hierarchy = "created"
    actions = [content_activate_and_enrol]

    @admin.display()
    def content_title(self, obj):
        try:
            return obj.object_data.get('title')
        except AttributeError:
            return ''

    class Meta:
        ordering = ["-created"]


class UserEnrolmentAdmin(admin.ModelAdmin):
    list_per_page = 20

    def get_queryset(self, request):
        # qs = super().get_queryset(request)
        return UserEnrolment.objects.select_related('user', 'content').all()

    list_display = ("content_title",
                    "user_full_name",
                    "sessions",
                    "progress",
                    "is_completed",
                    "completed_on",)
    list_filter = ("user__plan_id",
                   "content__content_type",
                   "content__is_active",
                   "content__on_demand",
                   "content__bookmarked",
                   "is_completed",
                   "updated",
                   "created",)
    search_fields = ("user__email",
                     "user__first_name",
                     "user__last_name",
                     "content__slug",
                     "content__description",)
    date_hierarchy = "created"

    @admin.display()
    def user_full_name(self, obj):
        return obj.user.full_name

    @admin.display()
    def sessions(self, obj):
        return len(obj.completed_sessions)

    @admin.display()
    def content_title(self, obj):
        try:
            return obj.content.object_data.get('title')
        except AttributeError:
            return ''

    class Meta:
        ordering = ["-updated"]


@admin.action(description="Process pending content")
def process_content(modeladmin, request, queryset):
    for content in queryset:
        try:
            user = User.objects.get(email=content.email)
            process_existing_content(
                content_type=content.content_type,
                object_data=content.object_data,
                user=user)
            content.is_active = False
            content.save()
        except ObjectDoesNotExist:
            print(f"User with email {content.email} does not exist")
            pass


class UserContentPendingAdmin(admin.ModelAdmin):
    list_per_page = 20
    #
    # def get_queryset(self, request):
    #     # qs = super().get_queryset(request)
    #     return UserContentPending.objects.all()

    list_display = ("email",
                    "slug",
                    "content_type",
                    "is_active",
                    "on_demand",
                    "chargebee_entity_id",)
    list_filter = ("content_type",
                   "is_active",
                   "on_demand",
                   "created",
                   "updated",)
    search_fields = ("email",
                     "slug",
                     "description",
                     "chargebee_entity_id",)
    date_hierarchy = "created"
    actions = [process_content]

    class Meta:
        ordering = ["-created"]


admin.site.register(UserContent, UserContentAdmin)
admin.site.register(UserContentPending, UserContentPendingAdmin)
admin.site.register(UserEnrolment, UserEnrolmentAdmin)
