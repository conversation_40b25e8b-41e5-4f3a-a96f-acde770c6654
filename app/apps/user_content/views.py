from rest_framework import status
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from django.contrib.auth import get_user_model
from django.shortcuts import get_object_or_404

from apps.authentication.permissions import (
    IsChargebeeRequest,
    <PERSON><PERSON>taff,
    ReadOnly,
)
from apps.users.permissions import MixedPermissionModelViewSet

from apps.user_content.models import UserContent
from apps.user_content.serializers import (
    UserContentSerializer,
    StaffUserContentSerializer,
    CurrentUserSerializer,
)

from utils.api.user_content import process_pending_content
from utils.meilisearch.router import (
    content_type_index_mapper as index_mapper
)

from apps.user_content.serializers import SlugUserContentSerializer



User = get_user_model()


class UserContentViewSet(MixedPermissionModelViewSet):
    # queryset = UserContent.objects.all()
    serializer_class = UserContentSerializer
    permission_classes = (ReadOnly,)
    # TODO: add pagination

    def get_permissions(self):
        is_authenticated = ['retrieve',
                            'list',
                            'courses',
                            'events',
                            'films',
                            'podcasts',
                            'articles',
                            'favourites',
                            'process_existing']
        is_staff = ['list',
                    'create',
                    'destroy',
                    'partial_update']
        if self.action in is_authenticated:
            return [IsAuthenticated()]
        if self.action in is_staff:
            return [IsStaff()]
        return [permission() for permission in self.permission_classes]

    def get_queryset(self):
        if self.request.user.is_staff:
            return UserContent.objects.all()
        return self.request.user.content.all()

    def get_serializer_class(self):
        if self.request.user.is_staff:
            return StaffUserContentSerializer
        return SlugUserContentSerializer

    def list(self, request):
        # return Response(self.get_serializer_class()(
        #     request.user.content.all(), many=True).data)
        user_content = request.user.content.all()

        return Response({
            "courses": SlugUserContentSerializer(user_content.filter(
                content_type='courses'), many=True).data,
            "events": SlugUserContentSerializer(user_content.filter(
                content_type='events'), many=True).data,
            "films": SlugUserContentSerializer(user_content.filter(
                content_type='films'), many=True).data,
            "podcasts": SlugUserContentSerializer(user_content.filter(
                content_type='podcasts'), many=True).data,
            "articles": SlugUserContentSerializer(user_content.filter(
                content_type='articles'), many=True).data,
        })

    def retrieve(self, request, pk):
        product = get_object_or_404(UserContent, user=request.user, pk=pk)
        content = index_mapper[product.content_type].get(product.slug)
        return Response(content)

    @action(detail=False, methods=['GET'])
    def courses(self, request, *args, **kwargs):
        return Response(self.get_serializer_class()(
            UserContent.courses_objects.filter(
                user=request.user), many=True).data)

    @action(detail=False, methods=['GET'])
    def events(self, request, *args, **kwargs):
        return Response(self.get_serializer_class()(
            UserContent.events_objects.filter(
                user=request.user), many=True).data)

    @action(detail=False, methods=['GET'])
    def films(self, request, *args, **kwargs):
        return Response(self.get_serializer_class()(
            UserContent.films_objects.filter(
                user=request.user), many=True).data)

    @action(detail=False, methods=['GET'])
    def podcasts(self, request, *args, **kwargs):
        return Response(self.get_serializer_class()(
            UserContent.podcasts_objects.filter(
                user=request.user), many=True).data)

    @action(detail=False, methods=['GET'])
    def articles(self, request, *args, **kwargs):
        return Response(self.get_serializer_class()(
            UserContent.articles_objects.filter(
                user=request.user), many=True).data)

    @action(detail=False, methods=['GET'])
    def entities(self, request, *args, **kwargs):
        return Response(self.get_serializer_class()(
            UserContent.entity_objects.filter(
                user=request.user), many=True).data)

    # @action(detail=False, methods=['GET'])
    # def categories(self, request, *args, **kwargs):
    #     return Response(self.get_serializer_class()(
    #         UserContent.category_objects.filter(
    #             user=request.user), many=True).data)

    @action(detail=False, methods=['GET'])
    def favourites(self, request, *args, **kwargs):
        return Response(self.get_serializer_class()(
            UserContent.bookmarked_objects.filter(
                user=request.user), many=True).data)

    @action(detail=False, methods=['GET'])
    def process_existing(self, request, *args, **kwargs):
        process_pending_content(request.user)

        return Response(CurrentUserSerializer(request.user).data)
