import uuid

from django.contrib.auth import get_user_model
from django.contrib.postgres.fields import <PERSON><PERSON>y<PERSON>ield
from django.db import models
from django.utils import timezone

from .managers import (
    CategoriesManager,
    CoursesManager,
    CoursesSessionsManager,
    EventsManager,
    EventsSessionsManager,
    FilmsManager,
    PodcastsManager,
    ArticlesManager,
    EntitiesManager,
    BookmarksManager,
)
from utils.meilisearch.router import (
    content_type_index_mapper as index_mapper
)



class UserContent(models.Model):
    id = models.UUIDField('ID',
        max_length=36, primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        get_user_model(), related_name='content', on_delete=models.CASCADE)
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    content_type = models.CharField(max_length=128)
    slug = models.CharField(max_length=256)
    index_id = models.CharField(max_length=256)
    description = models.CharField(max_length=256, null=True, blank=True)
    chargebee_entity_id = models.CharField(
        max_length=256, null=True, blank=True)

    # allow users that purchase content separately to have access independently
    # of the subscription
    on_demand = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)

    object_data = models.JSONField(null=True, blank=True)
    user_data = models.JSONField(null=True, blank=True)

    bookmarked = models.BooleanField(default=False)
    completed = models.BooleanField(default=False)

    objects = models.Manager()
    articles_objects = ArticlesManager()
    bookmarked_objects = BookmarksManager()
    category_objects = CategoriesManager()
    courses_objects = CoursesManager()
    entity_objects = EntitiesManager()
    events_objects = EventsManager()
    films_objects = FilmsManager()
    podcasts_objects = PodcastsManager()

    class Meta:
        verbose_name = 'User Content'
        verbose_name_plural = 'Users Content'
        # unique_together = ('user', 'content_type', 'index_id')

    def __str__(self):
        return f"[{self.content_type}][{self.description or self.slug}]"


class UserEnrolment(models.Model):
    id = models.UUIDField('ID',
        max_length=36, primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        get_user_model(), related_name='enrolments', on_delete=models.CASCADE)
    content = models.OneToOneField(
        UserContent, related_name='enrolment', on_delete=models.CASCADE)
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    # array of slugs for the completed content
    completed_sessions = ArrayField(
        models.CharField(max_length=256), default=list)
    progress = models.CharField(max_length=10, default='0')
    is_completed = models.BooleanField(default=False)
    completed_on = models.DateTimeField(null=True, blank=True)

    class Meta:
        verbose_name = 'User Enrolment'
        verbose_name_plural = 'Users Enrolment'
        # unique_together = ('user', 'content_type', 'index_id')

    def __str__(self):
        return f"[{self.content.content_type}][{self.content.slug}]" \
               f"[{self.progress}]"

    def calculate_progress_percentage(self):
        if self.is_completed:
            return 100

        content_type = self.content.content_type
        slug = self.content.slug
        sessions_ids = index_mapper[content_type].get_sessions_ids(slug)
        if len(sessions_ids):
            return int(len(self.completed_sessions) / len(sessions_ids) * 100)
        return 0

    def update_progress(self):
        progress = self.calculate_progress_percentage()
        if progress == 100:
            self.is_completed = True
            self.completed_on = timezone.now()
            self.content.completed = True
            self.content.save()
        self.progress = str(progress)
        self.save()


class SessionUserContent(models.Model):
    id = models.UUIDField('ID',
        max_length=36, primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        get_user_model(), related_name='sessions_content', on_delete=models.CASCADE)
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    content_type = models.CharField(max_length=128)
    slug = models.CharField(max_length=256)
    index_id = models.CharField(max_length=256)
    description = models.CharField(max_length=256, null=True, blank=True)

    object_data = models.JSONField(null=True, blank=True)
    bookmarked = models.BooleanField(default=False)
    completed = models.BooleanField(default=False)

    objects = models.Manager()
    bookmarked_objects = BookmarksManager()
    courses_objects = CoursesSessionsManager()
    events_objects = EventsSessionsManager()

    class Meta:
        verbose_name = 'User Sessions Content'
        verbose_name_plural = 'Users Sessions Content'

    def __str__(self):
        return f"[{self.content_type}][{self.description or self.slug}]"



class UserContentPending(models.Model):
    id = models.UUIDField('ID',
        max_length=36, primary_key=True, default=uuid.uuid4, editable=False)
    email = models.EmailField(max_length=256)
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    content_type = models.CharField(max_length=128)
    slug = models.CharField(max_length=256)
    index_id = models.CharField(max_length=256)
    description = models.CharField(max_length=256, null=True, blank=True)
    chargebee_entity_id = models.CharField(
        max_length=256, null=True, blank=True)

    # allow users that purchase content separately to have access independently
    # of the subscription
    on_demand = models.BooleanField(default=True)
    is_active = models.BooleanField(default=True)

    object_data = models.JSONField(null=True, blank=True)
    completed = models.BooleanField(default=True)

    class Meta:
        verbose_name = 'User Content Pending'
        verbose_name_plural = 'Users Content Pending'
        # unique_together = ('user', 'content_type', 'index_id')

    def __str__(self):
        return f"[{self.content_type}][{self.description or self.slug}]"
