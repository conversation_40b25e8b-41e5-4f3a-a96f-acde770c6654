from django.db import models


class CoursesManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(
            content_type='courses')


class EventsManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(
            content_type='events')


class FilmsManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(
            content_type='films')


class PodcastsManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(
            content_type='podcasts')


class ArticlesManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(
            content_type='articles')


class EntitiesManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(
            content_type='entities')


class CategoriesManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(
            content_type='categories')


class BookmarksManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(
            bookmarked=True)


class CoursesSessionsManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(
            content_type='course-sessions')


class EventsSessionsManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(
            content_type='event-sessions')
