from rest_framework import serializers
from rest_framework.exceptions import ValidationError

from django.contrib.auth import (
    get_user_model,
    password_validation as validators,
)

from config import settings

from apps.users.models import (
    Profile,
    SYNCED_USER_ATTRIBUTES,
)

from utils.chargebee.users import (
    update_user_details as chargebee_update_user_details,
)
from utils.hubspot.users import (
    create_contact as hubspot_create_contact,
    is_user_in_mailing_list as hubspot_is_user_in_mailing_list,
    subscribe_user,
    subscribe_new_user,
    update_user_details as hubspot_update_user_details,
    update_user_interests_from_mailing_lists,
)
from utils.nodebb.auth import (
    register_user as nodebb_register_user,
    update_user_profile_details as nodebb_update_user_details,
    add_user_email as nodebb_add_user_email,
)



User = get_user_model()


class UserSerializer(serializers.ModelSerializer):
    password = serializers.CharField(style={'input_type': 'password'}, write_only=True)
    has_billing = serializers.SerializerMethodField()

    class Meta:
        model = User
        # extra_kwargs = {'password': {'write_only': True}}
        fields = ('id',
                  'email',
                  'password',
                  'first_name',
                  'last_name',
                  'is_verified',
                  'plan_id',
                  'has_billing',)
                  # 'chargebee_id',
                  # 'is_staff',
                  # 'is_admin',
                  # 'last_login',
                  # 'created')

    def get_has_billing(self, instance):
        return bool(instance.chargebee_id)

    def to_representation(self, instance):
        return {
            'id': instance.id,
            'email': instance.email,
            'first_name': instance.first_name,
            'last_name': instance.last_name,
            'is_verified': instance.is_verified,
            'plan_id': instance.plan_id,
            'hasBilling': self.get_has_billing(instance),
        }

    def validate_email(self, value):
        # force lowercase
        return value.lower()

    def validate_password(self, value):
        try:
            validators.validate_password(value, self.instance)
        except ValidationError as exc:
            raise serializers.ValidationError({'detail': str(exc)})

        return value

    def create(self, validated_data):
        user = super().create(validated_data)
        user.set_password(validated_data['password'])
        user.save()
        avatar_generator_url = settings.AVATAR_GENERATOR_URL
        user_params = f"{user.first_name}+{user.last_name}".replace(' ', '')
        pic_url = f"{avatar_generator_url}{user_params}"
        user.profile.map_details({
            'nickname': user.first_name,
            'profile_photo': f"{pic_url}&background=ffe5e3&size=180",
            'profile_photo_tiny': f"{pic_url}&background=ffe5e3&size=48",
        })

        # triggers account activation workflow
        hubspot_create_contact(user)
        subscribe_new_user(user)
        if not user.subscribed_to_newsletter:
            user.subscribed_to_newsletter = hubspot_is_user_in_mailing_list(user)
            user.save()
        user.trigger_account_activation_flow()
        update_user_interests_from_mailing_lists(user)
        nodebb_register_user(user)
        nodebb_add_user_email(user)

        return user

    def update(self, instance, validated_data):
        # email_to_update = None
        # if validated_data.get('email', False):
        #     email_to_update = instance.email

        previous_email = instance.email if 'email' in validated_data else False
        user = super().update(instance, validated_data)
        if 'password' in validated_data:
            user.set_password(validated_data['password'])
            user.save()

        hubspot_update_user_details(user, previous_email=previous_email)
        chargebee_update_user_details(user)
        nodebb_update_user_details(user)

        if 'email' in validated_data:
            user.trigger_account_activation_flow(
                "email_update_confirmation_url")
            nodebb_add_user_email(user)

        # if email_to_update is not None:
        #     hubspot_change_user_email(user, email_to_update)
        #     chargebee_update_user_details(user)

        # any_sync_attrs_to_update = any([validated_data.get(attr, False) for
        #                                 attr in SYNCED_USER_ATTRIBUTES])
        # if any_sync_attrs_to_update:
        #     hubspot_update_user_details(user)
        #     chargebee_update_user_details(user)

        return user


class ChangePasswordSerializer(serializers.Serializer):
    model = User
    """
    Serializer for password change endpoint.
    """
    old_password = serializers.CharField(required=True)
    new_password = serializers.CharField(required=True)


class ResetPasswordSerializer(serializers.Serializer):
    model = User
    """
    Serializer for password change endpoint.
    """
    token = serializers.CharField(required=True)
    new_password = serializers.CharField(required=True)


class PublicProfileSerializer(serializers.ModelSerializer):
    class Meta:
        model = Profile
        fields = ('public_details',
                  'social_networks',
                  'interests')

    def to_representation(self, instance):
        return {
            'details': instance.public_details,
            'social_networks': instance.social_networks,
            'interests': instance.interests
        }


class MemberProfileSerializer(serializers.ModelSerializer):
    class Meta:
        model = Profile
        fields = ('member_details',
                  'social_networks',
                  'interests')

    def to_representation(self, instance):
        return {
            'details': instance.member_details,
            'social_networks': instance.social_networks,
            'interests': instance.interests
        }


class FriendsProfileSerializer(serializers.ModelSerializer):
    class Meta:
        model = Profile
        fields = ('friends_details',
                  'social_networks',
                  'interests')

    def to_representation(self, instance):
        return {
            'details': instance.friends_details,
            'social_networks': instance.social_networks,
            'interests': instance.interests
        }



class PrivateProfileSerializer(serializers.ModelSerializer):
    class Meta:
        model = Profile
        fields = ('private_details',
                  'social_networks',
                  'interests')

    def to_representation(self, instance):
        return {
            'details': instance.private_details,
            'social_networks': instance.social_networks,
            'interests': instance.interests
        }
