from utils.hubspot.client import (
    GLOBAL_USERS,
    FREE_USERS,
)


NEWSLETTER_MAP = {
    'general': GLOBAL_USERS,
    'monthly': GLOBAL_USERS,  # TODO: check what's monthly newsletter
    'membership': FREE_USERS,  # TODO: check membership newsletter
}

DEFAULT_NEWSLETTER_SUBSCRIPTION = dict(
    general=True,
    monthly=True,
    membership=True,
)

DEFAULT_PROFILE_PRIVACY = dict(
    first_name='private',
    last_name='private',
    nickname='private',
    birthdate='private',
    gender='private',
    location='private',
    mobile_phone='private',
    social_media='private',
    biography='private',
    profile_photo_tiny='private',
    profile_photo='private',
    cover_photo='private',
    interests='private',
)

DEFAULT_PROFILE_DETAILS = dict({k: '' for k in DEFAULT_PROFILE_PRIVACY.keys()})

DEFAULT_SOCIAL_NETWORKS = dict(
    facebook='',
    instagram='',
    youtube='',
)


def get_default_newsletter_subscription():
    return DEFAULT_NEWSLETTER_SUBSCRIPTION


def get_default_profile_privacy():
    return DEFAULT_PROFILE_PRIVACY


def get_default_profile_details():
    return DEFAULT_PROFILE_DETAILS


def get_default_social_networks():
    return DEFAULT_SOCIAL_NETWORKS
