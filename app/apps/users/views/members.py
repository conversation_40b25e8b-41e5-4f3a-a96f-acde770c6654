from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response


from django.contrib.auth import get_user_model
from django.db.models import Q
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.utils.text import slugify

from config.exceptions import (
    InvalidRequest,
    UserPermissionDenied,
)
from apps.authentication.permissions import (
    IsStaff,
    ReadOnly,
    IsStaffOrOwnAccount,
)

from apps.users.serializers import (
    PublicProfileSerializer,
    PrivateProfileSerializer,
    SimplePublicProfileSerializer,
)
from apps.user_content.models import (
    UserContent,
    SessionUserContent,
)
from apps.user_content.serializers import (
    UserContentResultsSerializer,
    SessionUserContentResultsSerializer,
    SlugUserContentSerializer,
)
from apps.users.permissions import MixedPermissionViewSet
from utils.cache.helpers import (
    get_cache,
    set_cache,
)
from config import settings

from apps.users.models import (
    Profile,
    profiles_cache_key,
)



User = get_user_model()


class MemberViewSet(MixedPermissionViewSet):
    permission_classes = (ReadOnly,)

    def get_permissions(self):
        is_annonymous = ['']
        is_authenticated = ['list',
                            'retrieve',
                            'follow',
                            'unfollow']
        is_staff_or_own_account = ['list',
                                   'retrieve',
                                   'follow',
                                   'unfollow']
        if self.action in is_annonymous:
            return [ReadOnly()]
        if self.action in is_authenticated:
            return [IsAuthenticated()]
        if self.action in is_staff_or_own_account:
            return [IsStaffOrOwnAccount()]
        return [permission() for permission in self.permission_classes]

    def get_serializer_class(self, pk=None):
        # if pk and self.request.user.profile.is_following(user__id=pk):
        #     return PrivateProfileSerializer
        if self.request.user.is_authenticated:
            return PublicProfileSerializer
        return SimplePublicProfileSerializer

    def list(self, request):
        # Get pagination parameters from query params
        limit = request.query_params.get('limit', 20)
        offset = request.query_params.get('offset', 0)
        search_query = request.query_params.get('search', '')

        try:
            limit = int(limit)
            offset = int(offset)
        except ValueError:
            limit = 20
            offset = 0

        cache_key = f"{profiles_cache_key}_limit_{limit}_offset_{offset}_search_{search_query}"
        cached_results = get_cache(cache_key)

        if cached_results:
            return Response(cached_results)
        else:
            queryset = Profile.objects.select_related('user').filter(
                user__is_active=True).exclude(
                Q(interests={}) |
                Q(public_details__profile_photo="") |
                Q(public_details__profile_photo__startswith=settings.AVATAR_GENERATOR_URL))

            if search_query:
                queryset = queryset.filter(
                    Q(public_details__first_name__icontains=search_query) |
                    Q(public_details__last_name__icontains=search_query) |
                    Q(public_details__nickname__icontains=search_query)
                )

            total_count = queryset.count()
            paginated_queryset = queryset[offset:offset + limit]

            serialized_data = SimplePublicProfileSerializer(
                paginated_queryset, many=True, context={'request': request}).data

            response = {
                'results': serialized_data,
                'total': total_count,
                'limit': limit,
                'offset': offset,
            }

            set_cache(cache_key, response, timeout=settings.MINIMUM_CACHE_TTL)

            return Response(response)

    def retrieve(self, request, *args, **kwargs):
        # if kwargs.get('pk') == str(request.user.id):
        #     # fetches own profile
        #     serializer = PrivateProfileSerializer(request.user.profile, context={'request': request})
        #     return Response(serializer.data)
        # else:
        user = get_object_or_404(User, pk=kwargs.get('pk'))
        return Response(PublicProfileSerializer(
            user.profile, context={'request': request}).data)

    @action(detail=True, methods=['GET'])
    def follow(self, request, pk):
        if pk != str(request.user.id):
            user_to_follow = get_object_or_404(User, pk=pk)
            request.user.follow(user_to_follow)
            return Response(PublicProfileSerializer(
                user_to_follow.profile, context={'request': request}).data)
        else:
            raise InvalidRequest(detail='Invalid request, please try again.')

    @action(detail=True, methods=['GET'])
    def unfollow(self, request, pk):
        if pk != str(request.user.id):
            user_to_unfollow = get_object_or_404(User, pk=pk)
            request.user.unfollow(user_to_unfollow)
            return Response(PublicProfileSerializer(
                user_to_unfollow.profile, context={'request': request}).data)
        else:
            raise InvalidRequest(detail='Invalid request, please try again.')
