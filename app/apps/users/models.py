import uuid

from django.contrib.auth.base_user import AbstractBaseUser
from django.contrib.auth import get_user_model
from django.contrib.auth.models import PermissionsMixin
from django.db import models
from django.contrib.postgres.fields import <PERSON><PERSON>y<PERSON>ield
from django.db.models.signals import post_save
from django.dispatch import receiver

from config import settings

from .constants import (
    NEWSLETTER_MAP,
    get_default_newsletter_subscription,
    get_default_profile_privacy,
    get_default_profile_details,
    get_default_social_networks,
)
from .managers import UserManager
from utils.hubspot.users import (
    update_activation_token,
    update_password_reset_token,
)

from utils.hubspot.users import (
    subscribe_user,
    unsubscribe_user,
)

from config.storage import UsersPublicMediaStorage



SYNCED_USER_ATTRIBUTES = ['first_name', 'last_name']


def generate_token():
    return uuid.uuid4().hex


class User(AbstractBaseUser, PermissionsMixin):
    id = models.UUIDField('ID',
        max_length=36, primary_key=True, default=uuid.uuid4, editable=False)
    email = models.EmailField(unique=True, max_length=128)
    first_name = models.CharField(max_length=32, default='/')
    last_name = models.CharField(max_length=32, default='/')
    created = models.DateTimeField(auto_now_add=True)
    is_staff = models.BooleanField(default=False)
    is_admin = models.BooleanField(default=False)

    # Chargebee
    chargebee_id = models.CharField(max_length=128, blank=True, null=True)
    subscription_id = models.CharField(max_length=128, blank=True, null=True)
    plan_id = models.CharField(
        max_length=128, default=settings.CHARGEBEE_FREE_PLAN_ID)

    # HubSpot
    hubspot_id = models.CharField(max_length=128, blank=True, null=True)
    subscribed_to_newsletter = models.BooleanField(default=False)

    # NodeBB
    community_user_id = models.CharField(max_length=128, blank=True, null=True)
    community_user_username = models.CharField(max_length=128, blank=True, null=True)
    community_groups = ArrayField(
        models.CharField(max_length=256), default=list)

    is_active = models.BooleanField(default=True)
    is_verified = models.BooleanField(default=False)
    activation_token = models.CharField(max_length=128, blank=True, null=True)
    password_reset_token = models.CharField(
        max_length=128, blank=True, null=True)

    objects = UserManager()
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = []


    class Meta:
        verbose_name = 'user'
        verbose_name_plural = 'users'


    @property
    def full_name(self):
        full_name = f'{self.first_name} {self.last_name}'
        return full_name.strip()

    def name_is_set(self):
        return self.first_name != '/' and self.last_name != '/'

    def __str__(self):
        return self.full_name

    def regenerate_activation_token(self):
        self.activation_token = generate_token()
        self.save()

    def regenerate_password_reset_token(self):
        self.password_reset_token = generate_token()
        self.save()

    def trigger_account_activation_flow(self, prop="account_activation_url"):
        """
        Regenerates the activation token and updates it triggering the user
        account activation workflow
        """
        self.regenerate_activation_token()
        update_activation_token(self, prop)

    def trigger_password_reset_flow(self):
        """
        Regenerates the password reset token and updates it triggering the user
        password reset workflow
        """
        self.regenerate_password_reset_token()
        update_password_reset_token(self)



class PlanOnHold(models.Model):
    created = models.DateTimeField(auto_now_add=True)
    email = models.CharField(max_length=256)
    chargebee_id = models.CharField(max_length=128, default='None')
    plan_id = models.CharField(max_length=128)
    subscription_id = models.CharField(max_length=128, blank=True, null=True)
    on_hold = models.BooleanField(default=True)
    event = models.JSONField()

    class Meta:
        verbose_name = 'Plan on hold'
        verbose_name_plural = 'Plans on hold'

class GiftPurchase(models.Model):
    created = models.DateTimeField(auto_now_add=True)
    buyer = models.ForeignKey(
        get_user_model(), related_name='gifts_purchased',
        on_delete=models.CASCADE)
    recipient = models.ForeignKey(
        get_user_model(), related_name='gifts_received',
        on_delete=models.CASCADE, blank=True, null=True)
    coupon_code = models.CharField(max_length=128)
    redeemed = models.BooleanField(default=False)


class Profile(models.Model):
    user = models.OneToOneField(
        get_user_model(), related_name='profile', on_delete=models.CASCADE)
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    picture = models.FileField(
        storage=UsersPublicMediaStorage(), null=True, blank=True)
    picture_tiny = models.FileField(
        storage=UsersPublicMediaStorage(), null=True, blank=True)
    cover = models.FileField(
        storage=UsersPublicMediaStorage(), null=True, blank=True)

    # attribute can have different levels of privacy rules:
    # public, members, friends and private
    privacy_rules = models.JSONField(default=get_default_profile_privacy)
    private_details = models.JSONField(default=get_default_profile_details)
    member_details = models.JSONField(default=get_default_profile_details)
    friends_details = models.JSONField(default=get_default_profile_details)
    public_details = models.JSONField(default=get_default_profile_details)

    interests = models.JSONField(default=dict)
    newsletters = models.JSONField(default=get_default_newsletter_subscription)
    social_networks = models.JSONField(default=get_default_social_networks)

    @property
    def full_name(self):
        return self.user.full_name

    def __str__(self):
        return f"{self.full_name}'s profile"

    def map_details(self, details=None):
        if not details:
            details = self.private_details
        else:
            self.private_details.update(details)

        details_map = {
            'private': {},
            'members': {},
            'friends': {},
            'public': {}
        }

        for attr, rule in self.privacy_rules.items():
            if attr in details.keys():
                details_map[rule][attr] = details[attr]
            else:
                details_map[rule][attr] = self.private_details.get(attr, '')

        details_map['public']['first_name'] = details.get(
            'first_name', self.user.first_name)
        details_map['public']['last_name'] = details.get(
            'last_name', self.user.last_name)

        if any(details.get(p, False) for p in ['first_name', 'last_name']):
            self.user.first_name = details_map['public']['first_name']
            self.user.last_name = details_map['public']['last_name']
            self.user.save()

        details_map['members'].update(details_map['public'])
        details_map['friends'].update(details_map['members'])
        details_map['private'].update(details_map['friends'])

        self.private_details = details_map['private']
        self.member_details = details_map['members']
        self.friends_details = details_map['friends']
        self.public_details = details_map['public']
        self.save()

    def update_newsletters(self, update_subscriptions):
        for newsletter_key, to_subscribe in update_subscriptions.items():
            if self.newsletters.get(newsletter_key) != to_subscribe:
                newsletter = NEWSLETTER_MAP.get(newsletter_key)
                if to_subscribe:
                    subscribe_user(self.user, newsletter)
                else:
                    unsubscribe_user(self.user, newsletter)

        self.newsletters = update_subscriptions
        self.save()


@receiver(post_save, sender=User)
def user_profile_creation_handler(sender, instance, created, **kwargs):
    if created:
        profile = Profile.objects.create(user=instance)
        profile.map_details()
