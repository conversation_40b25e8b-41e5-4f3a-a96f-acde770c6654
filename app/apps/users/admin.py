from django.contrib import admin
from .models import (
    GiftPurchase,
    User,
)

class UsersAdmin(admin.ModelAdmin):
    list_per_page = 20

    def get_queryset(self, request):
        # qs = super().get_queryset(request)
        return User.objects.prefetch_related("content").all()

    list_display = ("full_name",
                    "email",
                    "plan_id",
                    "hubspot_id",
                    "chargebee_id",
                    "community_user_username",
                    "purchases",)
    list_filter = ("plan_id",
                   "created",
                   # "is_teacher",
                   "is_active",
                   "is_verified",
                   "content__content_type",)
    search_fields = ("first_name",
                     "last_name",
                     "email",
                     "hubspot_id",
                     "chargebee_id",
                     "community_user_id",)
    date_hierarchy = "created"

    @admin.display()
    def purchases(self, obj):
        return obj.content.filter(on_demand=True).count()

    class Meta:
        ordering = ["-created"]


class GiftPurchaseAdmin(admin.ModelAdmin):
    list_per_page = 20

    list_display = ("buyer",
                    "recipient",
                    "coupon_code",
                    "redeemed",)
    list_filter = ("redeemed",
                   "created",)
    search_fields = ("buyer__email",
                     "buyer__first_name",
                     "buyer__last_name",
                     "recipient__email",
                     "recipient__first_name",
                     "recipient__last_name",
                     "coupon_code",)

    class Meta:
        ordering = ["-created"]


admin.site.register(User, UsersAdmin)
admin.site.register(GiftPurchase, GiftPurchaseAdmin)
