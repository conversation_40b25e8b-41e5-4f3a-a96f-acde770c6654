from rest_framework.decorators import action
from rest_framework.permissions import (
    AllowAny,
    IsAuthenticated,
)
from rest_framework import serializers
from rest_framework.response import Response

from django.http import HttpResponse

from config import settings
from config.exceptions import InvalidRequest

from apps.authentication.permissions import (
    IsChargebeeRequest,
    IsCMSRequest,
    IsDigitalOceanFunctionsRequest,
    IsStaff,
    IsStaffOrOwnAccount,
    ReadOnly,
)
from apps.users.permissions import MixedPermissionViewSet
from apps.user_content.models import UserContent

from utils.api.products import generate_gift_purchase
from utils.api.user_content import create_past_participants
from utils.chargebee.users import (
    create_portal_session,
    generate_single_purchase_hosted_page,
    generate_subscription_hosted_page,
    validate_coupon,
    retrieve_coupon_code,
    retrieve_portal_session,
    update_subscription_hosted_page,
)
from utils.chargebee.single_purchases import (
    event_dispatcher as purchases_event_dispatcher,
)
from utils.chargebee.subscriptions import (
    event_dispatcher as subscription_event_dispatcher,
)
from utils.chargebee.users import handle_abandoned_subscription_cart
from utils.sync.data_collector import collect_user_events, get_default_timestamps
from utils.sync.data_processor import process_sync_data



class DispatcherViewSet(MixedPermissionViewSet):
    permission_classes = (ReadOnly,)

    def get_permissions(self):
        is_annonymous = ['get_coupon']
        is_authenticated = ['portal_session',
                            'generate_subscription_checkout',
                            'update_subscription_checkout',
                            'generate_gift_checkout']
        is_chargebee_request = ['set_user_plan',
                                'set_purchase']
        is_cms_request = ['import_past_participants',
                          'update_content_slug']
        is_do_request = ['collect_sync_data',
                         'sync_data']
        is_staff = ['get_coupon',
                    'portal_session',
                    'generate_subscription_checkout',
                    'update_subscription_checkout']
        if self.action in is_annonymous:
            return [AllowAny()]
        if self.action in is_authenticated:
            return [IsAuthenticated()]
        # if self.action in is_staff_or_own_account:
        #     return [IsStaffOrOwnAccount()]
        if self.action in is_chargebee_request:
            return [IsChargebeeRequest()]
        if self.action in is_cms_request:
            return [IsCMSRequest()]
        if self.action in is_do_request:
            return [IsDigitalOceanFunctionsRequest()]
        if self.action in is_staff:
            return [IsStaff()]
        return [permission() for permission in self.permission_classes]

    @action(detail=False, methods=['POST'])
    def set_user_plan(self, request):
        event = request.data.get('event_type')

        if event in subscription_event_dispatcher.keys():
            subscription_event_dispatcher[event](request.data.get('content'))
        else:
            message = f"Chargebee webhook event '{event}' not implemented."
            if settings.SENTRY_ENABLED:
                from sentry_sdk import capture_message
                capture_message(message)
            print(message)

        return HttpResponse()

    @action(detail=False, methods=['POST'])
    def set_purchase(self, request):
        """Request from Chargebee webhook on single purchase update."""
        event = request.data.get('event_type')

        if event in purchases_event_dispatcher.keys():
            purchases_event_dispatcher[event](request.data.get('content'))
        else:
            message = f"Chargebee webhook event '{event}' not implemented."
            if settings.SENTRY_ENABLED:
                from sentry_sdk import capture_message
                capture_message(message)
            print(message)

        return HttpResponse()

    @action(detail=False, methods=['GET'])
    def get_coupon(self, request):
        code = request.query_params.get('code', False)
        if code:
            coupon = retrieve_coupon_code(code)
            if validate_coupon(coupon):
                return Response(coupon)
        raise serializers.ValidationError({'detail': 'Invalid coupon code.'})

    @action(detail=False, methods=['GET'])
    def portal_session(self, request):
        ps_id = request.query_params.get('portal_session_id', False)
        if ps_id:
            portal_data = retrieve_portal_session(ps_id)
        else:
            portal_data = create_portal_session(request.user)

        return Response(portal_data)

    @action(detail=False, methods=['POST'])
    def generate_subscription_checkout(self, request):
        hosted_page = generate_subscription_hosted_page(
            request.data, request.user)
        # TODO: defer handle_abandoned_subscription_cart
        handle_abandoned_subscription_cart(
            request.user, request.data.get("plan-id"))

        return Response(hosted_page)

    @action(detail=False, methods=['POST'])
    def update_subscription_checkout(self, request):
        hosted_page = update_subscription_hosted_page(
            request.data, request.user)

        return Response(hosted_page)

    @action(detail=False, methods=['POST'])
    def import_past_participants(self, request):
        data = request.data.get('participantsData')
        try:
            create_past_participants(
                data.get('contentType'),
                data.get('slug'),
                data.get('emailList'),
            )
            return Response()
        except Exception as e:
            error_message = f"Import past participants: {e}"
            print(error_message)
            if settings.SENTRY_ENABLED:
                from sentry_sdk import capture_message
                capture_message(error_message)
            raise InvalidRequest(detail=str(e))

    @action(detail=False, methods=['POST'])
    def generate_gift_checkout(self, request):
        hosted_page = generate_gift_purchase(
            request.data.get('chargebeeId'), request.user)

        return Response(hosted_page)

    @action(detail=False, methods=['POST'])
    def update_content_slug(self, request):
        data = request.data.get('contentData')
        content_type = data.get('contentType')
        previous_slug = data.get('previousSlug')
        new_slug = data.get('newSlug')
        try:
            if data.get('contentType') in [
                'courses', 'events', 'films', 'articles', 'podcasts', 'webinars']:
                # 'clubs', 'audio-journeys',
                updated_count = UserContent.objects.filter(
                    content_type=content_type, slug=previous_slug).update(
                    slug=new_slug)
                # TODO: invalidate cache when relevant
                print(f"update-content-slug: {content_type}/{previous_slug} -> "
                      f"{content_type}/{new_slug} | ({updated_count} entries)")
                return Response()
            else:
                print(f"update-content-slug: invalid content type "
                      f"'{content_type}': {previous_slug} -> {new_slug} ")
                raise InvalidRequest(
                    detail=f"Invalid content type: '{content_type}'. "
                           f"Slug update {previous_slug} -> {new_slug} "
                           f"ignored.")
        except Exception as e:
            error_message = f"update-content-slug: {e}"
            print(error_message)
            if settings.SENTRY_ENABLED:
                from sentry_sdk import capture_message
                capture_message(error_message)
            raise InvalidRequest(detail=str(e))

    @action(detail=False, methods=['POST'])
    def collect_sync_data(self, request):
        """
        Collect user-related events within a specified timestamp range.

        Payload:
        {
            "until_timestamp": "20231215_143000" (optional, defaults to now),
            "from_timestamp": "20231215_000000" (optional, defaults to beginning of current day)
        }
        """
        try:
            until_timestamp_str = request.data.get('until_timestamp')
            from_timestamp_str = request.data.get('from_timestamp')

            # Get timestamps
            if from_timestamp_str:
                from utils.sync.data_collector import parse_timestamp
                from_timestamp = parse_timestamp(from_timestamp_str)

                if until_timestamp_str:
                    until_timestamp = parse_timestamp(until_timestamp_str)
                else:
                    from datetime import datetime, timezone
                    until_timestamp = datetime.now(timezone.utc)
            else:
                from_timestamp, until_timestamp = get_default_timestamps(until_timestamp_str)

            # Collect events
            sync_data = collect_user_events(from_timestamp, until_timestamp)

            print(f"collect-sync-data: collected events from {from_timestamp.strftime('%Y%m%d_%H%M%S')} to {until_timestamp.strftime('%Y%m%d_%H%M%S')}")

            return Response(sync_data)

        except ValueError as e:
            raise InvalidRequest(detail=str(e))
        except Exception as e:
            error_message = f"collect-sync-data: {e}"
            print(error_message)
            if settings.SENTRY_ENABLED:
                from sentry_sdk import capture_message
                capture_message(error_message)
            raise InvalidRequest(detail=str(e))

    @action(detail=False, methods=['POST'])
    def sync_data(self, request):
        """
        Process synchronization data to create/update objects.

        Payload should contain the sync data structure returned by collect_sync_data.
        """
        try:
            sync_data = request.data

            if not sync_data:
                raise InvalidRequest(detail="No sync data provided")

            # Process the sync data
            results = process_sync_data(sync_data)

            print(f"sync-data: processed {sum(v for k, v in results.items() if k != 'errors')} items")

            if results.get('errors'):
                print(f"sync-data: {len(results['errors'])} errors occurred")
                for error in results['errors']:
                    print(f"sync-data error: {error}")

            return Response(results)

        except Exception as e:
            error_message = f"sync-data: {e}"
            print(error_message)
            if settings.SENTRY_ENABLED:
                from sentry_sdk import capture_message
                capture_message(error_message)
            raise InvalidRequest(detail=str(e))
