from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

from apps.authentication.permissions import (
    IsStaff,
    ReadOnly,
)
from apps.users.permissions import MixedPermissionViewSet

from .models import categories


# from utils.api.products import bookmark_content


class CategoriesViewSet(MixedPermissionViewSet):
    permission_classes = (ReadOnly,)

    def get_permissions(self):
        is_annonymous = ['list', 'retrieve']
        is_authenticated = ['list',
                            'retrieve']
                            # 'bookmark']
        is_staff = ['list',
                    'retrieve']
                    # 'bookmark']
        if self.action in is_annonymous:
            return [ReadOnly()]
        if self.action in is_authenticated:
            return [IsAuthenticated()]
        if self.action in is_staff:
            return [IsStaff()]
        return [permission() for permission in self.permission_classes]

    def list(self, request):
        # return Response(categories.list())

        # this was replaced in order to receive the result list as JSON
        response = categories.search({})
        results = response.get('hits', [])

        return Response(results)

    def retrieve(self, request, pk):
        return Response(categories.get(pk))

    # @action(detail=True, methods=['GET'])
    # def bookmark(self, request, pk):
    #     """
    #     Bookmards a category.
    #
    #     :param pk: category slug
    #     :return: serialized UserContent instance
    #     """
    #     entity = categories.get(pk)
    #     serialized_user_entity = bookmark_content(
    #         entity, categories.get_index_key(), request.user)
    #
    #     return Response(serialized_user_entity)
