from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

from django.http import Http404

from config import settings

from apps.authentication.permissions import (
    check_media_permissions,
    IsStaff,
    IsStaffOrOwnAccount,
    ReadOnly,
)
from config.exceptions import (
    PodcastAlreadyPurchased,
    PodcastNotFound,
    InvalidRequest,
)
from apps.users.permissions import MixedPermissionViewSet

from .models import podcasts

from utils.api.products import (
    bookmark_content,
    clean_product_for_seo,
    handle_product_purchase,
)

from utils.api.serializers import ContentResultsSerializer


class PodcastsViewSet(MixedPermissionViewSet):
    permission_classes = (ReadOnly,)

    def get_permissions(self):
        is_annonymous = ['list', 'retrieve']
        is_authenticated = ['favourite',
                            'purchase']
        is_staff = ['list',
                    'retrieve',
                    'favourite',
                    'purchase']
        if self.action in is_annonymous:
            return [ReadOnly()]
        if self.action in is_authenticated:
            return [IsAuthenticated()]
        if self.action in is_staff:
            return [IsStaff()]
        return [permission() for permission in self.permission_classes]

    def list(self, request):
        # if request.query_params:
        #     response = podcasts.search(request.query_params)
        # else:
        #     response = podcasts.list()

        # this was replaced in order to receive the result list as JSON
        response = podcasts.search(request.query_params)
        # results = response.get('hits', [])
        # return Response(results)

        return Response(ContentResultsSerializer(response).to_json())

    def retrieve(self, request, pk):
        try:
            podcast = podcasts.get(pk)
            if not check_media_permissions(request.user, podcast, 'podcast'):
                podcast = clean_product_for_seo(podcast)
            return Response(podcast)
        except Http404:
            raise PodcastNotFound()
        except Exception as e:
            error_message = f"Retrieveing podcast: {e}"
            print(error_message)
            if settings.SENTRY_ENABLED:
                from sentry_sdk import capture_message
                capture_message(error_message)
            raise InvalidRequest(detail=str(e))

    @action(detail=True, methods=['GET'])
    def purchase(self, request, pk):
        """
        Purchase a podcast as single purchase.

        If the user doesn't have a valid plan_id or hasn't purchased the
        podcast, the UserContent object should be created with the attribute
        is_active = False.

        :param pk: podcast slug
        :return: ok response if all goes well, forbidden if is not allowed
        """
        content_type = podcasts.get_index_key()
        if request.user.content.filter(
                content_type=content_type, slug=pk, is_active=True).exists():
            raise PodcastAlreadyPurchased()

        podcast = podcasts.get(pk)
        hosted_page = handle_product_purchase(
            podcast, content_type, request.user)
        # podcast['content_type'] = podcasts.get_index_key()
        # hosted_page = generate_single_purchase_hosted_page(
        #     podcast, request.user)

        return Response(hosted_page)

    @action(detail=True, methods=['POST'])
    def favourite(self, request, pk):
        """
        Bookmarks a podcast.

        :param pk: podcast slug
        :return: serialized UserContent instance
        """
        podcast = podcasts.get(pk)
        serialized_user_podcast = bookmark_content(
            podcast, podcasts.get_index_key(), request.user)

        return Response(serialized_user_podcast)
