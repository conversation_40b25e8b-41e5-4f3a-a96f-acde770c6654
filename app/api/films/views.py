from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

from django.http import Http404

from config import settings

from apps.authentication.permissions import (
    check_media_permissions,
    IsStaff,
    IsStaffOrOwnAccount,
    ReadOnly,
)
from config.exceptions import (
    FilmAlreadyPurchased,
    FilmNotFound,
    InvalidRequest,
)
from apps.users.permissions import MixedPermissionViewSet

from .models import films

from utils.api.products import (
    bookmark_content,
    clean_product_for_seo,
    handle_product_purchase,
)

from utils.api.serializers import ContentResultsSerializer


class FilmsViewSet(MixedPermissionViewSet):
    permission_classes = (ReadOnly,)

    def get_permissions(self):
        is_annonymous = ['list', 'retrieve']
        is_authenticated = ['favourite',
                            'purchase']
        is_staff = ['list',
                    'retrieve',
                    'favourite',
                    'purchase']
        if self.action in is_annonymous:
            return [ReadOnly()]
        if self.action in is_authenticated:
            return [IsAuthenticated()]
        if self.action in is_staff:
            return [IsStaff()]
        return [permission() for permission in self.permission_classes]

    def list(self, request):
        # if request.query_params:
        #     response = films.search(request.query_params)
        # else:
        #     response = films.list()

        # this was replaced in order to receive the result list as JSON
        response = films.search(request.query_params)
        # results = response.get('hits', [])
        # return Response(results)

        return Response(ContentResultsSerializer(response).to_json())

    def retrieve(self, request, pk):
        try:
            film = films.get(pk)
            if not check_media_permissions(request.user, film, 'film'):
                film = clean_product_for_seo(film)
            return Response(film)
        except Http404:
            raise FilmNotFound()
        except Exception as e:
            error_message = f"Retrieveing film: {e}"
            print(error_message)
            if settings.SENTRY_ENABLED:
                from sentry_sdk import capture_message
                capture_message(error_message)
            raise InvalidRequest(detail=str(e))

    @action(detail=True, methods=['GET'])
    def purchase(self, request, pk):
        """
        Purchase a film as single purchase.

        If the user doesn't have a valid plan_id or hasn't purchased the
        film, the UserContent object should be created with the attribute
        is_active = False.

        :param pk: film slug
        :return: ok response if all goes well, forbidden if is not allowed
        """
        content_type = films.get_index_key()
        if request.user.content.filter(
                content_type=content_type, slug=pk, is_active=True).exists():
            raise FilmAlreadyPurchased()

        film = films.get(pk)
        hosted_page = handle_product_purchase(
            film, content_type, request.user)
        # film['content_type'] = films.get_index_key()
        # hosted_page = generate_single_purchase_hosted_page(
        #     film, request.user)

        return Response(hosted_page)

    @action(detail=True, methods=['POST'])
    def favourite(self, request, pk):
        """
        Bookmarks a film.

        :param pk: film slug
        :return: serialized UserContent instance
        """
        film = films.get(pk)
        serialized_user_film = bookmark_content(
            film, films.get_index_key(), request.user)

        return Response(serialized_user_film)
