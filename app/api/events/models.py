from utils.meilisearch.indexer import (
    GenericIndexer,
    GenericSessionsIndexer,
)


class EventModel(GenericSessionsIndexer):
    pass

class EventSessionModel(GenericIndexer):
    pass


events = EventModel(
    index='events', indexed_id='event', sessions_key='sessions')
event_sessions = EventSessionModel(
    index='sessions', indexed_id='event-session')
get_sessions_ids = EventModel.get_sessions_ids_from_object
