from rest_framework import serializers
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

from django.core.exceptions import ObjectDoesNotExist
from django.http import (
    Http404,
    HttpResponse,
)

from config import settings

from apps.authentication.permissions import (
    check_event_permissions,
    IsStaff,
    IsStaffOrOwnAccount,
    ReadOnly,
)
from config.exceptions import (
    AlreadyRegisteredInEvent,
    EventAlreadyPurchased,
    EventNoPermissions,
    EventNotFound,
    NotRegisteredInEvent,
    InvalidRequest,
    UserPermissionDenied,
)
from apps.users.permissions import MixedPermissionViewSet

from .models import (
    events,
    event_sessions,
    get_sessions_ids,
)

from utils.meilisearch.helpers import filter_by_attribute
from utils.api.products import (
    content_type_bookmark_handler as bookmark_handler,
    clean_products_for_seo,
    handle_product_purchase,
    is_user_purchase,
)
from utils.api.enrolment import (
    enrol_user,
    is_user_enrolled,
    get_user_enrolment,
    update_progress,
)

from utils.api.serializers import (
    ContentResultsSerializer,
    EventSessionPublicSerializer,
)
from utils.hubspot.users import (
    map_user_latest_purchase as hubspot_map_user_latest_purchase,
    update_user_details as hubspot_update_user_details,
)


class EventsViewSet(MixedPermissionViewSet):
    permission_classes = (ReadOnly,)

    def get_permissions(self):
        is_annonymous = ['list', 'retrieve', 'sessions', 'series_sessions']
        is_authenticated = ['list',
                            'retrieve',
                            'favourite',
                            'purchase',
                            'register',
                            'mark_session_completed']
        is_staff = ['list',
                    'retrieve',
                    'update',
                    'partial_update',
                    'destroy',
                    'favourite',
                    'purchase',
                    'register',
                    'mark_session_completed']
        if self.action in is_annonymous:
            return [ReadOnly()]
        if self.action in is_authenticated:
            return [IsAuthenticated()]
        if self.action in is_staff:
            return [IsStaff()]
        return [permission() for permission in self.permission_classes]

    def list(self, request):
        # if request.query_params:
        #     response = events.search(request.query_params)
        # else:
        #     response = events.list()

        # this was replaced in order to receive the result list as JSON
        response = events.search(request.query_params)
        # results = response.get('hits', [])
        # return Response(results)

        return Response(ContentResultsSerializer(response).to_json())

    def retrieve(self, request, pk):
        try:
            return Response(events.get(pk))
        except Http404:
            raise EventNotFound()
        except Exception as e:
            error_message = f"Retrieveing event: {e}"
            print(error_message)
            if settings.SENTRY_ENABLED:
                from sentry_sdk import capture_message
                capture_message(error_message)
            raise InvalidRequest(detail=str(e))

    @action(detail=False, methods=['GET'])
    def series_sessions(self, request):
        try:
            response = event_sessions.search(request.query_params)
            results = response.pop('hits')
            return Response({
                **ContentResultsSerializer(response).to_json(),
                'results': EventSessionPublicSerializer(results, many=True).data
            })
        except Http404:
            return EventNotFound()
        except Exception as e:
            error_message = f"Retrieveing event series sessions: {e}"
            print(error_message)
            if settings.SENTRY_ENABLED:
                from sentry_sdk import capture_message
                capture_message(error_message)
            raise InvalidRequest(detail=str(e))

    @action(detail=True, methods=['GET'])
    def sessions(self, request, pk):
        """
        Lists the sessions for a given event.

        :param pk: event slug
        :return: list of event sessions
        """
        try:
            event = events.get(pk)
            sessions_ids = get_sessions_ids(event, events.sessions_key)
            response = event_sessions.search({
                'filter': filter_by_attribute(sessions_ids),
                'limit': 1000,
            })
            # results = response.get('hits', [])
            results = ContentResultsSerializer(response, sessions_ids).to_json()
            if not check_event_permissions(request.user, event):
                results['results'] = clean_products_for_seo(results['results'])
                # raise PermissionDenied(
                #     'You have no permissions to access this event.')
            return Response(results)
        except Http404:
            raise EventNotFound()
        except Exception as e:
            error_message = f"Retrieveing event sessions: {e}"
            print(error_message)
            if settings.SENTRY_ENABLED:
                from sentry_sdk import capture_message
                capture_message(error_message)
            raise InvalidRequest(detail=str(e))

    @action(detail=True, methods=['GET'])
    def purchase(self, request, pk):
        """
        Purchase a event as single purchase.

        If the user doesn't have a valid plan_id or hasn't purchased the
        event, the UserContent object should be created with the attribute
        is_active = False.

        :param pk: event slug
        :return: ok response if all goes well, forbidden if is not allowed
        """
        content_type = events.get_index_key()
        if is_user_purchase(request.user, content_type, pk):
            raise EventAlreadyPurchased()

        event = events.get(pk)
        if event.get('price') > 0:
            hosted_page = handle_product_purchase(
                event, content_type, request.user)

            return Response(hosted_page)
        raise UserPermissionDenied()

    @action(detail=True, methods=['GET'])
    def register(self, request, pk):
        """
        Enrols user in an event.

        If the user doesn't have a valid plan_id or hasn't purchased the
        event, the UserContent object should be created with the attribute
        is_active = False.

        :param pk: event slug
        :return: ok response if all goes well, forbidden if is not allowed
        """
        if is_user_enrolled(
                request.user, events.get_index_key(), pk):
            raise AlreadyRegisteredInEvent()

        event = events.get(pk)
        if not check_event_permissions(request.user, event):
            raise EventNoPermissions()
        enrolment = enrol_user(
            event, events.get_index_key(), request.user)

        hubspot_data = hubspot_map_user_latest_purchase(
            event, 'latest_event_registration')
        hubspot_update_user_details(request.user, hubspot_data)

        return HttpResponse()

    @action(detail=True, methods=['POST'])
    def favourite(self, request, pk):
        """
        Bookmarks an event or an event session.

        :param pk: event or event session slug
        :return: serialized UserContent instance
        """
        content_type = request.data.get('contentType', False)
        if not content_type:
            raise serializers.ValidationError(
                {'detail': 'The contentType is required.'})
        elif content_type == events.get_index_key():
            product = events.get(pk)
        elif content_type == event_sessions.get_indexed_id():
            product = event_sessions.get(pk)
        else:
            raise serializers.ValidationError(
                {'detail': 'Invalid contentType.'})
        serialized_user_event = bookmark_handler[content_type](
            product, content_type, request.user)

        return Response(serialized_user_event)

    @action(detail=True, methods=['POST'])
    def mark_session_completed(self, request, pk):
        """
        Completes an event session.

        :param pk: event slug
        :param data['session-slug']: session slug
        :return: serialized UserContent instance
        """
        try:
            new_progress = request.data.get('session_slug', False)
            if not new_progress:
                raise serializers.ValidationError(
                    {'detail': 'The session slug is required.'})
            user_enrolment = get_user_enrolment(
                request.user, events.get_index_key(), pk)
            update_progress(user_enrolment, new_progress)

            return HttpResponse()
        except ObjectDoesNotExist:
            raise NotRegisteredInEvent()
        except Exception as e:
            error_message = f"Mark session completed " \
                            f"{request.user.id}:{request.user.email} [{pk}]: {e}"
            print(error_message)
            if settings.SENTRY_ENABLED:
                from sentry_sdk import capture_message
                capture_message(error_message)
            raise InvalidRequest(detail='Invalid request, please try again.')
