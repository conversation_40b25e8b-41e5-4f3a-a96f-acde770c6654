from rest_framework.response import Response

from apps.authentication.permissions import (
    check_media_permissions,
    Is<PERSON>taff,
    IsStaffOrOwnAccount,
    ReadOnly,
)
from apps.users.permissions import MixedPermissionViewSet

from api.articles.models import articles
from api.categories.models import categories
from api.courses.models import courses
from api.events.models import events
from api.films.models import films
from api.podcasts.models import podcasts
from api.entities.models import entities
from utils.api.serializers import ContentResultsSerializer
from utils.meilisearch.indexer import MultiSearchClient



class SearchViewSet(MixedPermissionViewSet):
    permission_classes = (ReadOnly,)

    def get_permissions(self):
        is_annonymous = ['list']

        if self.action in is_annonymous:
            return [ReadOnly()]
        return [permission() for permission in self.permission_classes]

    def list(self, request):
        # articles_results = articles.search(request.query_params)
        # categories_results = categories.search(request.query_params)
        # courses_results = courses.search(request.query_params)
        # events_results = events.search(request.query_params)
        # films_results = films.search(request.query_params)
        # podcasts_results = podcasts.search(request.query_params)
        # entities_results = entities.search(request.query_params)
        # print(request.query_params)
        # response = {
        #     "articles": ContentResultsSerializer(articles_results).to_json(),
        #     "categories": ContentResultsSerializer(categories_results).to_json(),
        #     "courses": ContentResultsSerializer(courses_results).to_json(),
        #     "events": ContentResultsSerializer(events_results).to_json(),
        #     "films": ContentResultsSerializer(films_results).to_json(),
        #     "podcasts": ContentResultsSerializer(podcasts_results).to_json(),
        #     "teachers": ContentResultsSerializer(entities_results).to_json(),
        # }
        #
        # return Response(response)

        multisearch_client = MultiSearchClient([
            articles.get_index_key(),
            categories.get_index_key(),
            courses.get_index_key(),
            events.get_index_key(),
            films.get_index_key(),
            podcasts.get_index_key(),
            entities.get_index_key(),
        ])

        results = multisearch_client.search(request.query_params)

        response = {result.get('indexUid'): ContentResultsSerializer(
            result).to_json() for result in results}
        response['teachers'] = response.get('entities', [])
        del response['entities']

        return Response(response)
