from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

from django.http import (
    Http404,
    HttpResponse,
)

from config import settings
from config.exceptions import (
    EntityNotFound,
    InvalidRequest,
)

from apps.authentication.permissions import (
    check_event_permissions,
    IsStaff,
    IsStaffOrOwnAccount,
    ReadOnly,
)
from apps.users.permissions import MixedPermissionViewSet

from .models import entities

from utils.api.serializers import ContentResultsSerializer
from utils.api.products import bookmark_content


class EntitiesViewSet(MixedPermissionViewSet):
    permission_classes = (ReadOnly,)

    def get_permissions(self):
        is_annonymous = ['list', 'retrieve']
        is_authenticated = ['bookmark']
        is_staff = ['list',
                    'retrieve',
                    'bookmark']
        if self.action in is_annonymous:
            return [ReadOnly()]
        if self.action in is_authenticated:
            return [IsAuthenticated()]
        if self.action in is_staff:
            return [IsStaff()]
        return [permission() for permission in self.permission_classes]

    def list(self, request):
        # if request.query_params:
        #     response = entities.search(request.query_params)
        # else:
        #     response = entities.list()

        # this was replaced in order to receive the result list as JSON
        response = entities.search(request.query_params)

        return Response(ContentResultsSerializer(response).to_json())

    def retrieve(self, request, pk):
        try:
            return Response(entities.get(pk))
        except Http404:
            raise EntityNotFound()
        except Exception as e:
            error_message = f"Retrieveing entity: {e}"
            print(error_message)
            if settings.SENTRY_ENABLED:
                from sentry_sdk import capture_message
                capture_message(error_message)
            raise InvalidRequest(detail=str(e))

    @action(detail=True, methods=['POST'])
    def favourite(self, request, pk):
        """
        Bookmards an entity.

        :param pk: entity slug
        :return: serialized UserContent instance
        """
        entity = entities.get(pk)
        serialized_user_entity = bookmark_content(
            entity, entities.get_index_key(), request.user)

        return Response(serialized_user_entity)
