from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from django.http import (
    Http404,
    HttpResponse,
)

from apps.authentication.permissions import (
    check_media_permissions,
    IsStaff,
    IsStaffOrOwnAccount,
    ReadOnly,
)
from config.exceptions import (
    AlreadyRegisteredInWebinar,
    InvalidRequest,
    WebinarAlreadyPurchased,
    WebinarNotFound,
    WebinarNoPermissions,
)
from apps.users.permissions import MixedPermissionViewSet

from .models import webinars

from utils.api.enrolment import (
    enrol_user,
    is_user_enrolled,
)
from utils.api.products import (
    bookmark_content,
    clean_product_for_seo,
    handle_product_purchase,
)
from utils.api.serializers import ContentResultsSerializer
from utils.zoom.webinars import (
    join_meeting as zoom_join_meeting,
)



class WebinarsViewSet(MixedPermissionViewSet):
    permission_classes = (ReadOnly,)

    def get_permissions(self):
        is_annonymous = ['list', 'retrieve']
        is_authenticated = ['favourite',
                            'purchase',
                            'register',
                            'join']
        is_staff = ['list',
                    'retrieve',
                    'favourite',
                    'purchase',
                    'register',
                    'join']
        if self.action in is_annonymous:
            return [ReadOnly()]
        if self.action in is_authenticated:
            return [IsAuthenticated()]
        if self.action in is_staff:
            return [IsStaff()]
        return [permission() for permission in self.permission_classes]

    def list(self, request):
        # if request.query_params:
        #     response = webinars.search(request.query_params)
        # else:
        #     response = webinars.list()

        # this was replaced in order to receive the result list as JSON
        response = webinars.search(request.query_params)
        # results = response.get('hits', [])
        # return Response(results)

        return Response(ContentResultsSerializer(response).to_json())

    def retrieve(self, request, pk):
        try:
            webinar = webinars.get(pk)
            if not check_media_permissions(request.user, webinar, 'webinar'):
                webinar = clean_product_for_seo(webinar)
            return Response(webinar)
        except Http404:
            raise WebinarNotFound()
        except Exception as e:
            error_message = f"Retrieveing podcast: {e}"
            print(error_message)
            if settings.SENTRY_ENABLED:
                from sentry_sdk import capture_message
                capture_message(error_message)
            raise InvalidRequest(detail=str(e))
    #
    # @action(detail=True, methods=['GET'])
    # def purchase(self, request, pk):
    #     """
    #     Purchase a webinar as single purchase.
    #
    #     If the user doesn't have a valid plan_id or hasn't purchased the
    #     webinar, the UserContent object should be created with the attribute
    #     is_active = False.
    #
    #     :param pk: webinar slug
    #     :return: ok response if all goes well, forbidden if is not allowed
    #     """
    #     content_type = webinars.get_index_key()
    #     if request.user.content.filter(
    #             content_type=content_type, slug=pk, is_active=True).exists():
    #         raise ArticleAlreadyPurchased()
    #
    #     webinar = webinars.get(pk)
    #     hosted_page = handle_product_purchase(
    #         webinar, content_type, request.user)
    #     # webinar['content_type'] = webinars.get_index_key()
    #     # hosted_page = generate_single_purchase_hosted_page(
    #     #     webinar, request.user)
    #
    #     return Response(hosted_page)
    #

    @action(detail=True, methods=['GET'])
    def register(self, request, pk):
        """
        Enrols user in a webinar.

        If the user doesn't have a valid plan_id or hasn't purchased the
        webinar, the UserContent object should be created with the attribute
        is_active = False.

        :param pk: webinar slug
        :return: ok response if all goes well, forbidden if is not allowed
        """
        if is_user_enrolled(
                request.user, webinars.get_index_key(), pk):
            raise AlreadyRegisteredInWebinar()

        webinar = webinars.get(pk)
        if not check_media_permissions(request.user, webinar, 'webinar'):
            raise WebinarNoPermissions()
        enrol_user(webinar, webinars.get_index_key(), request.user)

        return HttpResponse()

    @action(detail=True, methods=['POST'])
    def favourite(self, request, pk):
        """
        Bookmarks an webinar.

        :param pk: webinar slug
        :return: serialized UserContent instance
        """
        webinar = webinars.get(pk)
        serialized_user_webinar = bookmark_content(
            webinar, webinars.get_index_key(), request.user)

        return Response(serialized_user_webinar)

    @action(detail=True, methods=['GET'])
    def join(self, request, pk):
        """
        Generate Zoom data to join webinar.

        :param pk: webinar slug
        :return: serialized Zoom meeting data
        """
        webinar = webinars.get(pk)
        serialized_meeting_data = zoom_join_meeting(webinar, request.user)

        return Response(serialized_meeting_data)
