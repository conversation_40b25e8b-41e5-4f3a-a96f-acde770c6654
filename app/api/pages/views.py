# from django.shortcuts import render

# from rest_framework import status
# from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import ViewSet

# from django.conf import settings

from .models import pages

from utils.api.pages import clean_page
# from utils.api.user_content import include_user_content



class PagesViewSet(ViewSet):
    def list(self, request):
        slug_list = [
            'about-us',
            'bursery',
            'community',
            'courses-landing',
            'events-landing',
            'faq-and-contact-us',
            'footer',
            'homepage',
            'interests-landing',
            'interests-by-speaker',
            'listen-landing',
            'membership',
            'partners',
            'read-landing',
            'recording-policy',
            'teachers',
            'terms-and-conditions',
            'testimonial',
            'privacy-policy',
            'watch-landing',
            'webinars-landing',
        ]
        return Response(slug_list)

    def retrieve(self, request, pk):
        # TODO: add caching
        page = pages.get(f"{pk}-{pk}", include_index=False)
        # TODO: add fallback
        clean_content = clean_page(page, request.user)
        # user_content = include_user_content(clean_content, request.user)

        return Response(clean_content)
