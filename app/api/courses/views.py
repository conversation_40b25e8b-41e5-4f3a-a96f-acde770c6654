from rest_framework import serializers
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

from django.core.exceptions import ObjectDoesNotExist
from django.http import (
    Http404,
    HttpResponse,
)

from config import settings

from apps.authentication.permissions import (
    check_course_permissions,
    IsStaff,
    IsStaffOrOwnAccount,
    ReadOnly,
)
from config.exceptions import (
    AlreadyRegisteredInCourse,
    CourseAlreadyPurchased,
    CourseNoPermissions,
    CourseNotFound,
    NotRegisteredInCourse,
    InvalidRequest,
)
from apps.users.permissions import MixedPermissionViewSet

from .models import (
    courses,
    course_sessions,
    get_sessions_ids,
)

from utils.meilisearch.helpers import filter_by_attribute
from utils.api.products import (
    bookmark_content,
    clean_products_for_seo,
    handle_product_purchase,
    is_user_purchase,
)
from utils.api.enrolment import (
    enrol_user,
    is_user_enrolled,
    get_user_enrolment,
    update_progress,
)

from utils.api.serializers import ContentResultsSerializer
from utils.hubspot.users import subscribe_user



class CoursesViewSet(MixedPermissionViewSet):
    permission_classes = (ReadOnly,)

    def get_permissions(self):
        is_annonymous = ['list', 'retrieve', 'sessions']
        is_authenticated = ['list',
                            'retrieve',
                            'favourite',
                            'purchase',
                            'register',
                            'mark_session_completed']
        is_staff = ['list',
                    'retrieve',
                    'update',
                    'partial_update',
                    'destroy',
                    'favourite',
                    'purchase',
                    'register',
                    'mark_session_completed']
        if self.action in is_annonymous:
            return [ReadOnly()]
        if self.action in is_authenticated:
            return [IsAuthenticated()]
        if self.action in is_staff:
            return [IsStaff()]
        return [permission() for permission in self.permission_classes]

    def list(self, request):
        # if request.query_params:
        #     response = courses.search(request.query_params)
        # else:
        #     response = courses.list()

        # this was replaced in order to receive the result list as JSON
        response = courses.search(request.query_params)
        # results = response.get('hits', [])
        # return Response(results)

        return Response(ContentResultsSerializer(response).to_json())

    def retrieve(self, request, pk):
        try:
            return Response(courses.get(pk))
        except Http404:
            raise CourseNotFound()
        except Exception as e:
            error_message = f"Retrieveing course: {e}"
            print(error_message)
            if settings.SENTRY_ENABLED:
                from sentry_sdk import capture_message
                capture_message(error_message)
            raise InvalidRequest(detail=str(e))

    @action(detail=True, methods=['GET'])
    def sessions(self, request, pk):
        """
        Lists the sessions for a given course.

        :param pk: course slug
        :return: list of course sessions
        """
        try:
            course = courses.get(pk)
            sessions_ids = get_sessions_ids(course, courses.sessions_key)
            response = course_sessions.search({
                'filter': filter_by_attribute(sessions_ids),
                'limit': 1000,
            })
            # results = response.get('hits', [])
            results = ContentResultsSerializer(response, sessions_ids).to_json()
            live_sessions = course.get('liveConversations', False)
            if live_sessions:
                live_sessions_ids = [session.get('slug') for session in live_sessions.get('sessions')]
                live_sessions_response = course_sessions.search({
                    'filter': filter_by_attribute(live_sessions_ids),
                    'limit': 1000,
                })
                # results = live_sessions_response.get('hits', [])
                results['liveConversations'] = ContentResultsSerializer(
                    live_sessions_response, live_sessions_ids).to_json()
            if not check_course_permissions(request.user, course):
                results['results'] = clean_products_for_seo(results['results'])
                if live_sessions:
                    results['liveConversations']['results'] = clean_products_for_seo(
                        results.get('liveConversations').get('results'))
                # raise PermissionDenied(
                #     'You have no permissions to access this course.')
            return Response(results)
        except Http404:
            raise CourseNotFound()
        except Exception as e:
            error_message = f"Retrieveing course sessions: {e}"
            print(error_message)
            if settings.SENTRY_ENABLED:
                from sentry_sdk import capture_message
                capture_message(error_message)
            raise InvalidRequest(detail=str(e))

    @action(detail=True, methods=['GET'])
    def purchase(self, request, pk):
        """
        Purchase a course as single purchase.

        If the user doesn't have a valid plan_id or hasn't purchased the
        course, the UserContent object should be created with the attribute
        is_active = False.

        :param pk: course slug
        :return: ok response if all goes well, forbidden if is not allowed
        """
        content_type = courses.get_index_key()
        if is_user_purchase(request.user, content_type, pk):
            raise CourseAlreadyPurchased()

        course = courses.get(pk)
        hosted_page = handle_product_purchase(
            course, content_type, request.user)

        return Response(hosted_page)

    @action(detail=True, methods=['GET'])
    def register(self, request, pk):
        """
        Enrols user in a course.

        If the user doesn't have a valid plan_id or hasn't purchased the
        course, the UserContent object should be created with the attribute
        is_active = False.

        :param pk: course slug
        :return: ok response if all goes well, forbidden if is not allowed
        """
        if is_user_enrolled(
                request.user, courses.get_index_key(), pk):
            raise AlreadyRegisteredInCourse()

        course = courses.get(pk)
        if not check_course_permissions(request.user, course):
            raise CourseNoPermissions()
        enrolment = enrol_user(
            course, courses.get_index_key(), request.user)

        # if course.get('hubspot_list_id', False):
        #     subscribe_user(request.user, course.get('hubspot_list_id'))

        return HttpResponse()

    @action(detail=True, methods=['POST'])
    def favourite(self, request, pk):
        """
        Bookmarks a course.

        :param pk: course slug
        :return: serialized UserContent instance
        """
        course = courses.get(pk)
        serialized_user_course = bookmark_content(
            course, courses.get_index_key(), request.user)

        return Response(serialized_user_course)

    @action(detail=True, methods=['POST'])
    def mark_session_completed(self, request, pk):
        """
        Completes a course lesson.

        :param pk: course slug
        :param data['session-slug']: session slug
        :return: serialized UserContent instance
        """
        try:
            user_enrolment = get_user_enrolment(
                request.user, courses.get_index_key(), pk)
            new_progress = request.data.get('session_slug', False)
            if not new_progress:
                raise serializers.ValidationError(
                    {'detail': 'The session_slug is required.'})
            update_progress(user_enrolment, new_progress)

            return HttpResponse()
        except ObjectDoesNotExist:
            raise NotRegisteredInCourse()
        except Exception as e:
            error_message = f"Mark session completed " \
                            f"{request.user.id}:{request.user.email} [{pk}]: {e}"
            print(error_message)
            if settings.SENTRY_ENABLED:
                from sentry_sdk import capture_message
                capture_message(error_message)
            raise InvalidRequest(detail='Invalid request, please try again.')
