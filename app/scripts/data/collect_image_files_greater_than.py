import pandas as pd
from collections import Counter

from api.courses.models import (
    courses,
    course_sessions,
)
from api.events.models import (
    events,
    event_sessions,
)
from api.films.models import films
from api.entities.models import entities
from api.articles.models import articles
from api.pages.models import pages

meta_props = ["url", "name", "size", "ext"]
MAXIMUM_FILE_SIZE = 500 # kb

pages_slug_list = [
    'about-us',
    'community',
    'courses-landing',
    'events-landing',
    'faq-and-contact-us',
    'footer',
    'homepage',
    'interests-landing',
    'interests-by-speaker',
    'listen-landing',
    'membership',
    'partners',
    'read-landing',
    # 'recording-policy',
    'teachers',
    # 'terms-and-conditions',
    # 'privacy-policy',
    'watch-landing',
    # 'webinars-landing',
]

def is_image(obj):
    return isinstance(obj, dict) and 'mime' in obj.keys() and 'image' in obj.get('mime')

def is_too_large(obj):
    return int(obj.get('size', MAXIMUM_FILE_SIZE)) >= MAXIMUM_FILE_SIZE

def is_wrong_format(obj):
    return 'webp' not in obj.get('ext')

def collect_image_info(obj, pages=False):
    pic_meta = {}
    for prop in obj.keys():
        img_obj = obj.get(prop)
        # if isinstance(img_obj, dict):
        #     list_values = filter(lambda value: isinstance(value, list), img_obj.values())
        #     for value in list(list_values):
        #         return collect_image_info(value)
        if is_image(img_obj) and (is_too_large(img_obj) or is_wrong_format(img_obj)):
            pic_meta['Title'] = obj.get('title', obj.get('name', obj.get('header', {}).get('title')))
            if pages:
                print(pic_meta['Title'])
                pic_meta['URL'] = f"https://advaya.life/{obj.get('slug')}"
            else:
                if obj.get('contentType') in ['course-sessions', 'event-sessions']:
                    pic_meta['URL'] = ""
                else:
                    pic_meta['URL'] = f"https://advaya.life/{obj.get('contentType', 'teachers')}/{obj.get('slug')}"
            for meta_prop in meta_props:
                pic_meta[f"Image {meta_prop}"] = img_obj.get(meta_prop)
            pic_meta["CMS field"] = prop
            pic_meta['Content Type'] = obj.get('contentType', 'teachers')
            pic_meta['Slug'] = obj.get('slug')
    return pic_meta




# courses_results = courses.search({'limit': 1000}).get('hits')
# events_results = events.search({'limit': 1000}).get('hits')
# films_results = films.search({'limit': 1000}).get('hits')
# articles_results = articles.search({'limit': 1000}).get('hits')
# entities_results = entities.search({'limit': 1000}).get('hits')
# course_sessions_results = course_sessions.search({'limit': 1000}).get('hits')
# event_sessions_results = event_sessions.search({'limit': 1000}).get('hits')
pages_results = [pages.get(f"{pk}-{pk}", include_index=False) for pk in pages_slug_list]

data = []

# for entry in [*courses_results, *events_results, *films_results, *articles_results, *entities_results]:
#     image_info = collect_image_info(entry)
#     if image_info:
#         data.append(list(image_info.values()))
#
# for entry in course_sessions_results:
#     entry['contentType'] = 'course-sessions'
#     image_info = collect_image_info(entry)
#     if image_info:
#         data.append(list(image_info.values()))
#
# for entry in event_sessions_results:
#     entry['contentType'] = 'event-sessions'
#     image_info = collect_image_info(entry)
#     if image_info:
#         data.append(list(image_info.values()))


try:
    for entry in pages_results:
        entry['contentType'] = f'pages/{entry.get('slug')}'
        print(entry['contentType'])
        image_info = collect_image_info(entry, pages=True)
        if image_info:
            data.append(list(image_info.values()))
        else:
            # if content type is pages, check inside the components and redo the
            # same proceadure recursively
            for prop in entry.keys():
                if isinstance(entry.get(prop), dict):
                    image_info = collect_image_info(entry.get(prop), pages=True)
                    if image_info:
                        data.append(list(image_info.values()))
except Exception as e:
    print(e)


df = pd.DataFrame(data, columns=['Title', 'URL', 'Image url', 'Image name', 'Image size', 'Image file extension', 'CMS field', 'Content Type', 'Slug'])

# output results as csv
df.to_csv('./large_image_files.csv', sep=';', index=False, encoding='utf-8')
