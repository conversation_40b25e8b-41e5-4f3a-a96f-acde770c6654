from rest_framework import routers

from apps.dispatcher.views import DispatcherViewSet
from apps.users.views.profiles import (
    ProfileViewSet,
    SettingsViewSet,
)
from apps.users.views.users import UserViewSet
from apps.user_content.views import UserContentViewSet
# from apps.user_content.views.enrolment import UserEnrolmentViewSetUserContentViewSet
from apps.user_data.views import UserDataViewSet

from api.pages.views import PagesViewSet
from api.categories.views import CategoriesViewSet

from api.entities.views import EntitiesViewSet

from api.articles.views import ArticlesViewSet
from api.podcasts.views import PodcastsViewSet
from api.films.views import FilmsViewSet
from api.courses.views import CoursesViewSet
from api.events.views import EventsViewSet
from api.webinars.views import WebinarsViewSet
from api.plans.views import PlansViewSet
from api.search.views import SearchViewSet



api = routers.DefaultRouter()
api.trailing_slash = '/?'

api.register(r'dispatcher', DispatcherViewSet, basename='dispatcher')
api.register(r'products', UserContentViewSet, basename='user_content')
api.register(r'settings', SettingsViewSet, basename='settings')
api.register(r'profiles', ProfileViewSet, basename='profiles')
api.register(r'users', UserViewSet)
api.register(r'data', UserDataViewSet, basename='user_data')

api.register(r'pages', PagesViewSet, basename='pages')
api.register(r'categories', CategoriesViewSet, basename='categories')
api.register(r'entities', EntitiesViewSet, basename='entities')
api.register(r'articles', ArticlesViewSet, basename='articles')
api.register(r'podcasts', PodcastsViewSet, basename='podcasts')
api.register(r'films', FilmsViewSet, basename='films')
api.register(r'courses', CoursesViewSet, basename='courses')
api.register(r'events', EventsViewSet, basename='events')
api.register(r'practice', EventsViewSet, basename='practice')
api.register(r'webinars', WebinarsViewSet, basename='webinars')
api.register(r'plans', PlansViewSet, basename='plans')
api.register(r'search', SearchViewSet, basename='search')
