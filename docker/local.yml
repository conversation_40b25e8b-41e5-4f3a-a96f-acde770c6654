version: "3.9"

services:
  db:
    container_name: api_db
    image: postgres:14-alpine
    restart: always
    env_file:
      - .envs/.local/.db.env
    expose:
      - 5432
    ports:
      - "5432:5432"
    volumes:
      - db-data:/var/lib/postgresql/data

  redis:
    container_name: api_cache
    image: redis:alpine
    restart: always
    expose:
      - 6379
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    entrypoint: redis-server --appendonly yes --port 6379

  api:
    container_name: api_django
    build:
      context: ../app
      dockerfile: ../app/Dockerfile
    command: python manage.py runserver 0.0.0.0:8000
    volumes:
      - ../app:/app
    ports:
      - "8000:8000"
    env_file:
      - .envs/.local/.db.env
      - .envs/.local/.api.env
    depends_on:
      - db
      - redis

  celery:
    container_name: api_queue
    build:
      context: ../app
    command: celery --app config worker --loglevel=info
    volumes:
      - ../app:/app
    env_file:
      - .envs/.local/.db.env
      - .envs/.local/.api.env
    depends_on:
      - api
      - redis

volumes:
  db-data:
    driver: local
  redis-data:
